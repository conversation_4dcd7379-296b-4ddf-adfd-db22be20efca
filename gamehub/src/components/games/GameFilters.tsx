import {
  Card,
  CardBody,
  Input,
  Button,
  Select,
  SelectItem,
} from '@heroui/react';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';

interface GameFiltersProps {
  searchTerm: string;
  selectedGenre: string;
  selectedPlatform: string;
  minRating: string;
  sortBy: string;
  sortOrder: string;
  genres: string[];
  platforms: string[];
  onSearchChange: (value: string) => void;
  onGenreChange: (value: string) => void;
  onPlatformChange: (value: string) => void;
  onMinRatingChange: (value: string) => void;
  onSortChange: (sortBy: string, sortOrder: string) => void;
  onClearFilters: () => void;
}

export default function GameFilters({
  searchTerm,
  selectedGenre,
  selectedPlatform,
  minRating,
  sortBy,
  sortOrder,
  genres,
  platforms,
  onSearchChange,
  onGenreChange,
  onPlatformChange,
  onMinRatingChange,
  onSortChange,
  onClearFilters,
}: GameFiltersProps) {
  const hasActiveFilters = searchTerm || selectedGenre || selectedPlatform || minRating;

  const handleSortChange = (value: string) => {
    const [field, order] = value.split('-');
    onSortChange(field, order);
  };

  return (
    <Card>
      <CardBody className="p-6">
        <div className="flex flex-col gap-4">
          {/* Search Bar */}
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              placeholder="Buscar jogos, desenvolvedores, gêneros..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
              className="flex-1"
              size="lg"
            />
            <Button
              color="primary"
              variant="flat"
              startContent={<FunnelIcon className="w-4 h-4" />}
              className="md:w-auto"
            >
              Filtros Avançados
            </Button>
          </div>
          
          {/* Filter Row */}
          <div className="flex flex-col md:flex-row gap-4">
            <Select
              placeholder="Gênero"
              value={selectedGenre}
              onChange={(e) => onGenreChange(e.target.value)}
              className="flex-1"
            >
              <SelectItem key="" value="">Todos os gêneros</SelectItem>
              {genres.map((genre) => (
                <SelectItem key={genre} value={genre}>
                  {genre}
                </SelectItem>
              ))}
            </Select>
            
            <Select
              placeholder="Plataforma"
              value={selectedPlatform}
              onChange={(e) => onPlatformChange(e.target.value)}
              className="flex-1"
            >
              <SelectItem key="" value="">Todas as plataformas</SelectItem>
              {platforms.map((platform) => (
                <SelectItem key={platform} value={platform}>
                  {platform}
                </SelectItem>
              ))}
            </Select>
            
            <Select
              placeholder="Avaliação mínima"
              value={minRating}
              onChange={(e) => onMinRatingChange(e.target.value)}
              className="flex-1"
            >
              <SelectItem key="" value="">Qualquer avaliação</SelectItem>
              <SelectItem key="4.5" value="4.5">4.5+ estrelas</SelectItem>
              <SelectItem key="4.0" value="4.0">4.0+ estrelas</SelectItem>
              <SelectItem key="3.5" value="3.5">3.5+ estrelas</SelectItem>
              <SelectItem key="3.0" value="3.0">3.0+ estrelas</SelectItem>
            </Select>
            
            <Select
              placeholder="Ordenar por"
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => handleSortChange(e.target.value)}
              className="flex-1"
            >
              <SelectItem key="playerCount-desc" value="playerCount-desc">
                Mais jogados
              </SelectItem>
              <SelectItem key="rating-desc" value="rating-desc">
                Melhor avaliados
              </SelectItem>
              <SelectItem key="title-asc" value="title-asc">
                Nome (A-Z)
              </SelectItem>
              <SelectItem key="releaseDate-desc" value="releaseDate-desc">
                Mais recentes
              </SelectItem>
            </Select>
            
            {hasActiveFilters && (
              <Button
                color="default"
                variant="flat"
                onClick={onClearFilters}
                className="md:w-auto"
              >
                Limpar
              </Button>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

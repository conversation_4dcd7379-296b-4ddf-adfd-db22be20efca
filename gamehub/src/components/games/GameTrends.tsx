import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Chip,
  Progress,
  Button,
  Tabs,
  Tab,
  Avatar,
  Divider,
} from '@heroui/react';
import {
  TrendingUpIcon,
  ArrowTrendingDownIcon,
  FireIcon,
  SparklesIcon,
  ClockIcon,
  CalendarDaysIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';

import { Game } from '@/types/database';
import { GameCard } from '@/components/games';
import { mockGames } from '@/data/mockGames';

interface TrendData {
  game: Game;
  trend: 'up' | 'down' | 'stable';
  changePercentage: number;
  previousRank?: number;
  currentRank: number;
}

interface GameTrendsProps {
  onGameClick?: (game: Game) => void;
  onToggleFavorite?: (gameId: string) => void;
  userFavorites?: string[];
  showFavoriteButton?: boolean;
}

export default function GameTrends({
  onGameClick,
  onToggleFavorite,
  userFavorites = [],
  showFavoriteButton = false,
}: GameTrendsProps) {
  const [trendingGames, setTrendingGames] = useState<TrendData[]>([]);
  const [newReleases, setNewReleases] = useState<Game[]>([]);
  const [risingGames, setRisingGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTrendData();
  }, []);

  const loadTrendData = async () => {
    try {
      setLoading(true);
      
      // Generate trending data
      const trending: TrendData[] = mockGames
        .map((game, index) => ({
          game: { ...game, id: `trending-${index}` },
          trend: Math.random() > 0.3 ? 'up' : Math.random() > 0.5 ? 'down' : 'stable',
          changePercentage: Math.floor(Math.random() * 50) + 1,
          previousRank: index + Math.floor(Math.random() * 5) + 1,
          currentRank: index + 1,
        }))
        .slice(0, 10);
      
      // Generate new releases (games from last 30 days)
      const releases = mockGames
        .filter(() => Math.random() > 0.7) // Simulate some games being recent
        .slice(0, 6)
        .map((game, index) => ({ ...game, id: `release-${index}` }));
      
      // Generate rising games (games with significant growth)
      const rising = mockGames
        .filter(() => Math.random() > 0.6)
        .slice(0, 8)
        .map((game, index) => ({ ...game, id: `rising-${index}` }));
      
      setTrendingGames(trending);
      setNewReleases(releases);
      setRisingGames(rising);
    } catch (error) {
      console.error('Error loading trend data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon className="w-4 h-4 text-success" />;
      case 'down':
        return <ArrowTrendingDownIcon className="w-4 h-4 text-danger" />;
      default:
        return <div className="w-4 h-4 bg-default-400 rounded-full" />;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'success';
      case 'down':
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <div className="space-y-6">
      <Tabs
        variant="underlined"
        classNames={{
          tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
          cursor: "w-full bg-primary",
          tab: "max-w-fit px-0 h-12",
        }}
      >
        <Tab
          key="trending"
          title={
            <div className="flex items-center gap-2">
              <FireIcon className="w-4 h-4" />
              <span>Em Alta</span>
            </div>
          }
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-2">
                <FireIcon className="w-5 h-5 text-orange-500" />
                <h3 className="text-lg font-semibold">Jogos em Alta</h3>
              </div>
              <Chip color="warning" variant="flat" size="sm">
                Atualizado agora
              </Chip>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-3">
                {trendingGames.map((item, index) => (
                  <div
                    key={item.game.id}
                    className="flex items-center gap-4 p-3 rounded-lg hover:bg-default-100 cursor-pointer transition-colors"
                    onClick={() => onGameClick?.(item.game)}
                  >
                    {/* Rank */}
                    <div className="flex items-center gap-2 w-16">
                      <span className="text-lg font-bold text-default-500">
                        #{item.currentRank}
                      </span>
                      {getTrendIcon(item.trend)}
                    </div>
                    
                    {/* Game Info */}
                    <div className="flex items-center gap-3 flex-1">
                      <Avatar
                        src={item.game.images.cover}
                        alt={item.game.title}
                        className="w-12 h-12"
                        radius="sm"
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold text-sm">{item.game.title}</h4>
                        <p className="text-xs text-default-500">{item.game.developer}</p>
                        <div className="flex items-center gap-2 mt-1">
                          {item.game.genres.slice(0, 2).map((genre) => (
                            <Chip key={genre} size="sm" variant="flat" color="primary">
                              {genre}
                            </Chip>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    {/* Trend Data */}
                    <div className="text-right">
                      <Chip
                        size="sm"
                        color={getTrendColor(item.trend) as any}
                        variant="flat"
                      >
                        {item.trend === 'up' ? '+' : item.trend === 'down' ? '-' : ''}
                        {item.changePercentage}%
                      </Chip>
                      <p className="text-xs text-default-500 mt-1">
                        {item.game.playerCount.toLocaleString()} jogadores
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </Tab>

        <Tab
          key="new"
          title={
            <div className="flex items-center gap-2">
              <SparklesIcon className="w-4 h-4" />
              <span>Novos</span>
            </div>
          }
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-2">
                <SparklesIcon className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold">Lançamentos Recentes</h3>
              </div>
              <Button
                variant="flat"
                color="primary"
                size="sm"
                endContent={<ArrowRightIcon className="w-4 h-4" />}
              >
                Ver Todos
              </Button>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {newReleases.map((game) => (
                  <GameCard
                    key={game.id}
                    game={game}
                    variant="compact"
                    isFavorite={userFavorites.includes(game.id)}
                    showFavoriteButton={showFavoriteButton}
                    onGameClick={onGameClick}
                    onToggleFavorite={onToggleFavorite}
                  />
                ))}
              </div>
            </CardBody>
          </Card>
        </Tab>

        <Tab
          key="rising"
          title={
            <div className="flex items-center gap-2">
              <TrendingUpIcon className="w-4 h-4" />
              <span>Em Ascensão</span>
            </div>
          }
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUpIcon className="w-5 h-5 text-success" />
                <h3 className="text-lg font-semibold">Jogos em Ascensão</h3>
              </div>
              <Chip color="success" variant="flat" size="sm">
                Crescimento rápido
              </Chip>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {risingGames.map((game) => (
                  <GameCard
                    key={game.id}
                    game={game}
                    variant="compact"
                    isFavorite={userFavorites.includes(game.id)}
                    showFavoriteButton={showFavoriteButton}
                    onGameClick={onGameClick}
                    onToggleFavorite={onToggleFavorite}
                  />
                ))}
              </div>
            </CardBody>
          </Card>
        </Tab>

        <Tab
          key="insights"
          title={
            <div className="flex items-center gap-2">
              <CalendarDaysIcon className="w-4 h-4" />
              <span>Insights</span>
            </div>
          }
        >
          <div className="space-y-6">
            {/* Weekly Summary */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <ClockIcon className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold">Resumo da Semana</h3>
                </div>
              </CardHeader>
              <CardBody className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-success/10 rounded-lg">
                    <p className="text-2xl font-bold text-success">+15%</p>
                    <p className="text-sm text-default-600">Novos jogadores</p>
                  </div>
                  <div className="text-center p-4 bg-primary/10 rounded-lg">
                    <p className="text-2xl font-bold text-primary">8</p>
                    <p className="text-sm text-default-600">Novos lançamentos</p>
                  </div>
                  <div className="text-center p-4 bg-warning/10 rounded-lg">
                    <p className="text-2xl font-bold text-warning">4.2★</p>
                    <p className="text-sm text-default-600">Avaliação média</p>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Genre Trends */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Tendências por Gênero</h3>
              </CardHeader>
              <CardBody className="space-y-3">
                {[
                  { genre: 'Battle Royale', growth: 25, color: 'success' },
                  { genre: 'MOBA', growth: 15, color: 'primary' },
                  { genre: 'FPS', growth: 10, color: 'secondary' },
                  { genre: 'RPG', growth: 5, color: 'warning' },
                  { genre: 'Strategy', growth: -5, color: 'danger' },
                ].map((item) => (
                  <div key={item.genre} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="font-medium">{item.genre}</span>
                      <Chip
                        size="sm"
                        color={item.color as any}
                        variant="flat"
                      >
                        {item.growth > 0 ? '+' : ''}{item.growth}%
                      </Chip>
                    </div>
                    <Progress
                      value={Math.abs(item.growth)}
                      maxValue={30}
                      color={item.color as any}
                      className="w-32"
                      size="sm"
                    />
                  </div>
                ))}
              </CardBody>
            </Card>
          </div>
        </Tab>
      </Tabs>
    </div>
  );
}

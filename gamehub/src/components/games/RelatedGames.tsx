import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@heroui/react';
import {
  ArrowRightIcon,
} from '@heroicons/react/24/outline';

import { Game } from '@/types/database';
import { GameCard } from '@/components/games';
import { mockGames } from '@/data/mockGames';

interface RelatedGamesProps {
  currentGame: Game;
  onGameClick?: (game: Game) => void;
  onToggleFavorite?: (gameId: string) => void;
  userFavorites?: string[];
  showFavoriteButton?: boolean;
}

export default function RelatedGames({
  currentGame,
  onGameClick,
  onToggleFavorite,
  userFavorites = [],
  showFavoriteButton = false,
}: RelatedGamesProps) {
  const [relatedGames, setRelatedGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRelatedGames();
  }, [currentGame.id]);

  const loadRelatedGames = async () => {
    try {
      setLoading(true);
      
      // Algorithm to find related games based on:
      // 1. Same genres
      // 2. Same developer/publisher
      // 3. Similar tags
      // 4. Similar player count range
      
      const related = mockGames
        .filter(game => game.title !== currentGame.title) // Exclude current game
        .map(game => {
          let score = 0;
          
          // Genre similarity (highest weight)
          const sharedGenres = game.genres.filter(genre => 
            currentGame.genres.includes(genre)
          ).length;
          score += sharedGenres * 3;
          
          // Developer/Publisher similarity
          if (game.developer === currentGame.developer) score += 2;
          if (game.publisher === currentGame.publisher) score += 1;
          
          // Tag similarity
          const sharedTags = game.tags.filter(tag => 
            currentGame.tags.includes(tag)
          ).length;
          score += sharedTags * 1;
          
          // Platform similarity
          const sharedPlatforms = game.platforms.filter(platform => 
            currentGame.platforms.includes(platform)
          ).length;
          score += sharedPlatforms * 0.5;
          
          // Player count similarity (games with similar popularity)
          const playerCountDiff = Math.abs(game.playerCount - currentGame.playerCount);
          const maxPlayerCount = Math.max(game.playerCount, currentGame.playerCount);
          const playerCountSimilarity = 1 - (playerCountDiff / maxPlayerCount);
          score += playerCountSimilarity * 1;
          
          // Rating similarity
          const ratingDiff = Math.abs(game.rating - currentGame.rating);
          const ratingSimilarity = 1 - (ratingDiff / 5);
          score += ratingSimilarity * 0.5;
          
          return { ...game, similarityScore: score };
        })
        .sort((a, b) => b.similarityScore - a.similarityScore)
        .slice(0, 6) // Get top 6 most similar games
        .map((game, index) => ({ ...game, id: `related-${index}-${Date.now()}` }));
      
      setRelatedGames(related);
    } catch (error) {
      console.error('Error loading related games:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Jogos Relacionados</h3>
        </CardHeader>
        <CardBody className="flex justify-center py-8">
          <Spinner size="lg" />
        </CardBody>
      </Card>
    );
  }

  if (relatedGames.length === 0) {
    return (
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Jogos Relacionados</h3>
        </CardHeader>
        <CardBody className="text-center py-8">
          <p className="text-default-500">
            Nenhum jogo relacionado encontrado.
          </p>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <h3 className="text-lg font-semibold">Jogos Relacionados</h3>
        <Button
          variant="flat"
          color="primary"
          size="sm"
          endContent={<ArrowRightIcon className="w-4 h-4" />}
        >
          Ver Mais
        </Button>
      </CardHeader>
      <CardBody className="pt-0">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {relatedGames.map((game) => (
            <GameCard
              key={game.id}
              game={game}
              variant="compact"
              isFavorite={userFavorites.includes(game.id)}
              showFavoriteButton={showFavoriteButton}
              onGameClick={onGameClick}
              onToggleFavorite={onToggleFavorite}
            />
          ))}
        </div>
        
        {/* Similarity Explanation */}
        <div className="mt-4 p-3 bg-default-100 rounded-lg">
          <p className="text-sm text-default-600">
            <strong>Por que estes jogos são relacionados?</strong>
          </p>
          <p className="text-xs text-default-500 mt-1">
            Baseado em gêneros similares, mesmo desenvolvedor, tags compartilhadas e popularidade similar.
          </p>
        </div>
      </CardBody>
    </Card>
  );
}

// Component for "More Like This" section
interface MoreLikeThisProps {
  currentGame: Game;
  onGameClick?: (game: Game) => void;
  onToggleFavorite?: (gameId: string) => void;
  userFavorites?: string[];
  showFavoriteButton?: boolean;
}

export function MoreLikeThis({
  currentGame,
  onGameClick,
  onToggleFavorite,
  userFavorites = [],
  showFavoriteButton = false,
}: MoreLikeThisProps) {
  const [recommendations, setRecommendations] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecommendations();
  }, [currentGame.id]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      
      // More sophisticated recommendation algorithm
      // This could integrate with machine learning models in production
      
      const recs = mockGames
        .filter(game => game.title !== currentGame.title)
        .map(game => {
          let score = 0;
          
          // Primary genre match (very important)
          if (currentGame.genres.length > 0 && game.genres.includes(currentGame.genres[0])) {
            score += 5;
          }
          
          // Secondary genre matches
          const otherGenreMatches = game.genres.filter((genre, index) => 
            index > 0 && currentGame.genres.includes(genre)
          ).length;
          score += otherGenreMatches * 2;
          
          // Developer reputation (games from same dev)
          if (game.developer === currentGame.developer) score += 3;
          
          // Rating similarity (users who like high-rated games tend to like other high-rated games)
          if (Math.abs(game.rating - currentGame.rating) <= 0.5) score += 2;
          
          // Platform availability (if user plays on specific platform)
          const platformMatch = game.platforms.some(platform => 
            currentGame.platforms.includes(platform)
          );
          if (platformMatch) score += 1;
          
          // Popularity tier (similar player base size)
          const currentTier = getPopularityTier(currentGame.playerCount);
          const gameTier = getPopularityTier(game.playerCount);
          if (currentTier === gameTier) score += 1;
          
          return { ...game, recommendationScore: score };
        })
        .sort((a, b) => b.recommendationScore - a.recommendationScore)
        .slice(0, 4)
        .map((game, index) => ({ ...game, id: `rec-${index}-${Date.now()}` }));
      
      setRecommendations(recs);
    } catch (error) {
      console.error('Error loading recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPopularityTier = (playerCount: number): string => {
    if (playerCount >= 500000) return 'massive';
    if (playerCount >= 100000) return 'popular';
    if (playerCount >= 50000) return 'moderate';
    return 'niche';
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="text-md font-semibold">Mais jogos como este</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {recommendations.map((game) => (
          <GameCard
            key={game.id}
            game={game}
            variant="compact"
            isFavorite={userFavorites.includes(game.id)}
            showFavoriteButton={showFavoriteButton}
            onGameClick={onGameClick}
            onToggleFavorite={onToggleFavorite}
          />
        ))}
      </div>
    </div>
  );
}

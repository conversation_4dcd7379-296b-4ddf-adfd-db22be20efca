import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Textarea,
  Divider,
  Chip,
  Progress,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from '@heroui/react';
import {
  StarIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  ChatBubbleLeftIcon,
} from '@heroicons/react/24/outline';
import {
  StarIcon as StarSolidIcon,
  HandThumbUpIcon as ThumbUpSolidIcon,
  HandThumbDownIcon as ThumbDownSolidIcon,
} from '@heroicons/react/24/solid';

import { useAuth } from '@/contexts/AuthContext';

interface GameReview {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  gameId: string;
  rating: number;
  title: string;
  content: string;
  helpful: number;
  notHelpful: number;
  createdAt: Date;
  updatedAt: Date;
  isRecommended: boolean;
  hoursPlayed?: number;
  platform: string;
  userVote?: 'helpful' | 'not_helpful' | null;
}

interface GameReviewsProps {
  gameId: string;
  gameTitle: string;
}

export default function GameReviews({ gameId, gameTitle }: GameReviewsProps) {
  const { currentUser } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [reviews, setReviews] = useState<GameReview[]>([]);
  const [userReview, setUserReview] = useState<GameReview | null>(null);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'helpful' | 'rating'>('helpful');
  
  // New review form
  const [newReview, setNewReview] = useState({
    rating: 0,
    title: '',
    content: '',
    isRecommended: true,
    hoursPlayed: 0,
    platform: 'PC',
  });

  // Mock reviews data
  const mockReviews: GameReview[] = [
    {
      id: '1',
      userId: 'user1',
      userName: 'GamerPro2024',
      userAvatar: 'https://i.pravatar.cc/150?u=user1',
      gameId,
      rating: 5,
      title: 'Jogo incrível!',
      content: 'Este jogo superou todas as minhas expectativas. A jogabilidade é fluida, os gráficos são impressionantes e a comunidade é muito ativa. Recomendo para todos os fãs do gênero.',
      helpful: 24,
      notHelpful: 2,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      isRecommended: true,
      hoursPlayed: 150,
      platform: 'PC',
    },
    {
      id: '2',
      userId: 'user2',
      userName: 'CasualPlayer',
      userAvatar: 'https://i.pravatar.cc/150?u=user2',
      gameId,
      rating: 4,
      title: 'Muito bom, mas pode melhorar',
      content: 'O jogo é muito divertido e viciante. A única coisa que me incomoda são alguns bugs menores e a falta de conteúdo para jogadores solo. Mas no geral, vale muito a pena.',
      helpful: 18,
      notHelpful: 5,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-10'),
      isRecommended: true,
      hoursPlayed: 75,
      platform: 'PlayStation',
    },
    {
      id: '3',
      userId: 'user3',
      userName: 'CompetitiveGamer',
      userAvatar: 'https://i.pravatar.cc/150?u=user3',
      gameId,
      rating: 3,
      title: 'Mediano',
      content: 'O jogo tem potencial, mas ainda precisa de muito trabalho. O matchmaking é inconsistente e há problemas de balanceamento. Espero que os desenvolvedores corrijam esses problemas em breve.',
      helpful: 12,
      notHelpful: 8,
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-05'),
      isRecommended: false,
      hoursPlayed: 45,
      platform: 'PC',
    },
  ];

  useEffect(() => {
    loadReviews();
  }, [gameId, sortBy]);

  const loadReviews = async () => {
    try {
      setLoading(true);
      
      // For demo purposes, use mock data
      let sortedReviews = [...mockReviews];
      
      switch (sortBy) {
        case 'newest':
          sortedReviews.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
          break;
        case 'oldest':
          sortedReviews.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
          break;
        case 'helpful':
          sortedReviews.sort((a, b) => b.helpful - a.helpful);
          break;
        case 'rating':
          sortedReviews.sort((a, b) => b.rating - a.rating);
          break;
      }
      
      setReviews(sortedReviews);
      
      // Check if current user has a review
      if (currentUser) {
        const existingReview = sortedReviews.find(r => r.userId === currentUser.uid);
        setUserReview(existingReview || null);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    const stars = [];
    
    for (let i = 1; i <= 5; i++) {
      const isFilled = i <= rating;
      const StarComponent = isFilled ? StarSolidIcon : StarIcon;
      
      stars.push(
        <StarComponent
          key={i}
          className={`w-5 h-5 ${isFilled ? 'text-yellow-500' : 'text-gray-400'} ${
            interactive ? 'cursor-pointer hover:text-yellow-400' : ''
          }`}
          onClick={interactive && onRatingChange ? () => onRatingChange(i) : undefined}
        />
      );
    }
    
    return <div className="flex gap-1">{stars}</div>;
  };

  const handleVoteReview = async (reviewId: string, voteType: 'helpful' | 'not_helpful') => {
    if (!currentUser) return;
    
    // Update review vote (mock implementation)
    setReviews(prev => prev.map(review => {
      if (review.id === reviewId) {
        const wasHelpful = review.userVote === 'helpful';
        const wasNotHelpful = review.userVote === 'not_helpful';
        
        let newHelpful = review.helpful;
        let newNotHelpful = review.notHelpful;
        
        // Remove previous vote
        if (wasHelpful) newHelpful--;
        if (wasNotHelpful) newNotHelpful--;
        
        // Add new vote if different
        if (voteType !== review.userVote) {
          if (voteType === 'helpful') newHelpful++;
          else newNotHelpful++;
        }
        
        return {
          ...review,
          helpful: newHelpful,
          notHelpful: newNotHelpful,
          userVote: voteType === review.userVote ? null : voteType,
        };
      }
      return review;
    }));
  };

  const handleSubmitReview = async () => {
    if (!currentUser || !newReview.rating || !newReview.title || !newReview.content) return;
    
    const review: GameReview = {
      id: `review-${Date.now()}`,
      userId: currentUser.uid,
      userName: currentUser.displayName || 'Usuário',
      userAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      gameId,
      rating: newReview.rating,
      title: newReview.title,
      content: newReview.content,
      helpful: 0,
      notHelpful: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      isRecommended: newReview.isRecommended,
      hoursPlayed: newReview.hoursPlayed,
      platform: newReview.platform,
    };
    
    setReviews(prev => [review, ...prev]);
    setUserReview(review);
    setNewReview({
      rating: 0,
      title: '',
      content: '',
      isRecommended: true,
      hoursPlayed: 0,
      platform: 'PC',
    });
    onClose();
  };

  const getReviewStats = () => {
    const total = reviews.length;
    const ratings = [5, 4, 3, 2, 1].map(rating => ({
      rating,
      count: reviews.filter(r => r.rating === rating).length,
      percentage: total > 0 ? (reviews.filter(r => r.rating === rating).length / total) * 100 : 0,
    }));
    
    const averageRating = total > 0 
      ? reviews.reduce((sum, r) => sum + r.rating, 0) / total 
      : 0;
    
    const recommendedPercentage = total > 0 
      ? (reviews.filter(r => r.isRecommended).length / total) * 100 
      : 0;
    
    return { ratings, averageRating, recommendedPercentage, total };
  };

  const stats = getReviewStats();

  return (
    <div className="space-y-6">
      {/* Review Stats */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <h3 className="text-lg font-semibold">Avaliações da Comunidade</h3>
            {currentUser && !userReview && (
              <Button color="primary" onPress={onOpen}>
                Escrever Avaliação
              </Button>
            )}
          </div>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Overall Rating */}
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {stats.averageRating.toFixed(1)}
              </div>
              {renderStars(Math.round(stats.averageRating))}
              <p className="text-sm text-default-500 mt-2">
                {stats.total} avaliações
              </p>
            </div>
            
            {/* Rating Distribution */}
            <div className="space-y-2">
              {stats.ratings.map(({ rating, count, percentage }) => (
                <div key={rating} className="flex items-center gap-2">
                  <span className="text-sm w-8">{rating}★</span>
                  <Progress
                    value={percentage}
                    className="flex-1"
                    color="warning"
                    size="sm"
                  />
                  <span className="text-sm text-default-500 w-8">{count}</span>
                </div>
              ))}
            </div>
            
            {/* Recommendation */}
            <div className="text-center">
              <div className="text-2xl font-bold text-success mb-2">
                {stats.recommendedPercentage.toFixed(0)}%
              </div>
              <p className="text-sm text-default-500">
                dos jogadores recomendam
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Sort Options */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-default-500">Ordenar por:</span>
        {[
          { key: 'helpful', label: 'Mais úteis' },
          { key: 'newest', label: 'Mais recentes' },
          { key: 'rating', label: 'Melhor avaliadas' },
          { key: 'oldest', label: 'Mais antigas' },
        ].map(({ key, label }) => (
          <Chip
            key={key}
            variant={sortBy === key ? 'solid' : 'flat'}
            color="primary"
            className="cursor-pointer"
            onClick={() => setSortBy(key as any)}
          >
            {label}
          </Chip>
        ))}
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id}>
            <CardBody className="space-y-4">
              {/* Review Header */}
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Avatar src={review.userAvatar} name={review.userName} size="sm" />
                  <div>
                    <p className="font-semibold text-sm">{review.userName}</p>
                    <div className="flex items-center gap-2 text-xs text-default-500">
                      <span>{review.hoursPlayed}h jogadas</span>
                      <span>•</span>
                      <span>{review.platform}</span>
                      <span>•</span>
                      <span>{review.createdAt.toLocaleDateString('pt-BR')}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {renderStars(review.rating)}
                  <Chip
                    size="sm"
                    color={review.isRecommended ? 'success' : 'danger'}
                    variant="flat"
                  >
                    {review.isRecommended ? 'Recomenda' : 'Não recomenda'}
                  </Chip>
                </div>
              </div>
              
              {/* Review Content */}
              <div>
                <h4 className="font-semibold mb-2">{review.title}</h4>
                <p className="text-default-600 leading-relaxed">{review.content}</p>
              </div>
              
              {/* Review Actions */}
              <div className="flex items-center justify-between pt-2 border-t border-divider">
                <div className="flex items-center gap-4">
                  <Button
                    size="sm"
                    variant="flat"
                    startContent={
                      review.userVote === 'helpful' ? (
                        <ThumbUpSolidIcon className="w-4 h-4" />
                      ) : (
                        <HandThumbUpIcon className="w-4 h-4" />
                      )
                    }
                    onClick={() => handleVoteReview(review.id, 'helpful')}
                    color={review.userVote === 'helpful' ? 'success' : 'default'}
                  >
                    Útil ({review.helpful})
                  </Button>
                  <Button
                    size="sm"
                    variant="flat"
                    startContent={
                      review.userVote === 'not_helpful' ? (
                        <ThumbDownSolidIcon className="w-4 h-4" />
                      ) : (
                        <HandThumbDownIcon className="w-4 h-4" />
                      )
                    }
                    onClick={() => handleVoteReview(review.id, 'not_helpful')}
                    color={review.userVote === 'not_helpful' ? 'danger' : 'default'}
                  >
                    Não útil ({review.notHelpful})
                  </Button>
                </div>
                <Button
                  size="sm"
                  variant="flat"
                  startContent={<ChatBubbleLeftIcon className="w-4 h-4" />}
                >
                  Responder
                </Button>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Write Review Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <h3>Escrever Avaliação - {gameTitle}</h3>
          </ModalHeader>
          <ModalBody className="space-y-4">
            {/* Rating */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Sua avaliação *
              </label>
              {renderStars(newReview.rating, true, (rating) =>
                setNewReview(prev => ({ ...prev, rating }))
              )}
            </div>

            {/* Recommendation */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Você recomenda este jogo?
              </label>
              <div className="flex gap-2">
                <Chip
                  variant={newReview.isRecommended ? 'solid' : 'flat'}
                  color="success"
                  className="cursor-pointer"
                  onClick={() => setNewReview(prev => ({ ...prev, isRecommended: true }))}
                >
                  👍 Sim, recomendo
                </Chip>
                <Chip
                  variant={!newReview.isRecommended ? 'solid' : 'flat'}
                  color="danger"
                  className="cursor-pointer"
                  onClick={() => setNewReview(prev => ({ ...prev, isRecommended: false }))}
                >
                  👎 Não recomendo
                </Chip>
              </div>
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Título da avaliação *
              </label>
              <input
                type="text"
                value={newReview.title}
                onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Resuma sua experiência..."
                className="w-full px-3 py-2 border border-divider rounded-lg bg-content1 focus:outline-none focus:ring-2 focus:ring-primary"
                maxLength={100}
              />
              <p className="text-xs text-default-500 mt-1">
                {newReview.title.length}/100 caracteres
              </p>
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Sua avaliação *
              </label>
              <Textarea
                value={newReview.content}
                onChange={(e) => setNewReview(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Conte sobre sua experiência com o jogo. O que você gostou? O que poderia ser melhor?"
                minRows={4}
                maxRows={8}
                maxLength={1000}
              />
              <p className="text-xs text-default-500 mt-1">
                {newReview.content.length}/1000 caracteres
              </p>
            </div>

            {/* Additional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Horas jogadas
                </label>
                <input
                  type="number"
                  value={newReview.hoursPlayed}
                  onChange={(e) => setNewReview(prev => ({
                    ...prev,
                    hoursPlayed: parseInt(e.target.value) || 0
                  }))}
                  placeholder="0"
                  min="0"
                  className="w-full px-3 py-2 border border-divider rounded-lg bg-content1 focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Plataforma
                </label>
                <select
                  value={newReview.platform}
                  onChange={(e) => setNewReview(prev => ({ ...prev, platform: e.target.value }))}
                  className="w-full px-3 py-2 border border-divider rounded-lg bg-content1 focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="PC">PC</option>
                  <option value="PlayStation">PlayStation</option>
                  <option value="Xbox">Xbox</option>
                  <option value="Nintendo Switch">Nintendo Switch</option>
                  <option value="Mobile">Mobile</option>
                </select>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onClose}>
              Cancelar
            </Button>
            <Button
              color="primary"
              onPress={handleSubmitReview}
              isDisabled={!newReview.rating || !newReview.title || !newReview.content}
            >
              Publicar Avaliação
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}

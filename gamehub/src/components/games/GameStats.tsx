import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Header,
} from '@heroui/react';
import {
  PlayIcon,
  UsersIcon,
  StarIcon,
  TrophyIcon,
} from '@heroicons/react/24/outline';

import { Game } from '@/types/database';

interface GameStatsProps {
  games: Game[];
  genres: string[];
  platforms: string[];
}

export default function GameStats({ games, genres, platforms }: GameStatsProps) {
  const formatPlayerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(0)}K`;
    }
    return count.toString();
  };

  const totalPlayers = games.reduce((sum, game) => sum + game.playerCount, 0);
  const averageRating = games.length > 0 
    ? (games.reduce((sum, game) => sum + game.rating, 0) / games.length).toFixed(1)
    : '0.0';

  const getGenreStats = () => {
    return genres.slice(0, 8).map((genre) => {
      const genreCount = games.filter(game => 
        game.genres.includes(genre)
      ).length;
      
      return { name: genre, count: genreCount };
    });
  };

  const getPlatformStats = () => {
    return platforms.slice(0, 8).map((platform) => {
      const platformCount = games.filter(game => 
        game.platforms.includes(platform)
      ).length;
      
      return { name: platform, count: platformCount };
    });
  };

  return (
    <div className="space-y-6">
      {/* Gaming Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardBody className="text-center p-4">
            <PlayIcon className="w-8 h-8 text-primary mx-auto mb-2" />
            <p className="text-2xl font-bold text-primary">
              {games.length}
            </p>
            <p className="text-sm text-default-500">Jogos Disponíveis</p>
          </CardBody>
        </Card>
        
        <Card>
          <CardBody className="text-center p-4">
            <UsersIcon className="w-8 h-8 text-success mx-auto mb-2" />
            <p className="text-2xl font-bold text-success">
              {formatPlayerCount(totalPlayers)}
            </p>
            <p className="text-sm text-default-500">Jogadores Ativos</p>
          </CardBody>
        </Card>
        
        <Card>
          <CardBody className="text-center p-4">
            <StarIcon className="w-8 h-8 text-warning mx-auto mb-2" />
            <p className="text-2xl font-bold text-warning">
              {averageRating}
            </p>
            <p className="text-sm text-default-500">Avaliação Média</p>
          </CardBody>
        </Card>
        
        <Card>
          <CardBody className="text-center p-4">
            <TrophyIcon className="w-8 h-8 text-secondary mx-auto mb-2" />
            <p className="text-2xl font-bold text-secondary">
              {genres.length}
            </p>
            <p className="text-sm text-default-500">Gêneros</p>
          </CardBody>
        </Card>
      </div>

      {/* Top Genres */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Gêneros Mais Populares</h3>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {getGenreStats().map((genre) => (
              <Card key={genre.name} isPressable className="hover:scale-105 transition-transform">
                <CardBody className="text-center p-4">
                  <p className="font-semibold text-sm mb-1">{genre.name}</p>
                  <p className="text-xs text-default-500">
                    {genre.count} jogos
                  </p>
                </CardBody>
              </Card>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Top Platforms */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Plataformas Mais Populares</h3>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {getPlatformStats().map((platform) => (
              <Card key={platform.name} isPressable className="hover:scale-105 transition-transform">
                <CardBody className="text-center p-4">
                  <p className="font-semibold text-sm mb-1">{platform.name}</p>
                  <p className="text-xs text-default-500">
                    {platform.count} jogos
                  </p>
                </CardBody>
              </Card>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
}

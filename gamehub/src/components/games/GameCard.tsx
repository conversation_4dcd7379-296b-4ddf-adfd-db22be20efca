import {
  Card,
  CardBody,
  <PERSON>ton,
  Chip,
  Badge,
  Image,
} from '@heroui/react';
import {
  HeartIcon,
  UsersIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartSolidIcon,
  StarIcon as StarSolidIcon,
} from '@heroicons/react/24/solid';

import { Game } from '@/types/database';

interface GameCardProps {
  game: Game;
  isFavorite?: boolean;
  showFavoriteButton?: boolean;
  onGameClick?: (game: Game) => void;
  onToggleFavorite?: (gameId: string) => void;
  variant?: 'default' | 'compact' | 'detailed';
}

export default function GameCard({
  game,
  isFavorite = false,
  showFavoriteButton = false,
  onGameClick,
  onToggleFavorite,
  variant = 'default'
}: GameCardProps) {
  const formatPlayerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(0)}K`;
    }
    return count.toString();
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <StarSolidIcon key={i} className="w-4 h-4 text-yellow-500" />
      );
    }
    
    if (hasHalfStar) {
      stars.push(
        <StarIcon key="half" className="w-4 h-4 text-yellow-500" />
      );
    }
    
    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <StarIcon key={`empty-${i}`} className="w-4 h-4 text-gray-400" />
      );
    }
    
    return stars;
  };

  const handleCardClick = () => {
    if (onGameClick) {
      onGameClick(game);
    }
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onToggleFavorite) {
      onToggleFavorite(game.id);
    }
  };

  if (variant === 'compact') {
    return (
      <Card
        isPressable={!!onGameClick}
        onPress={handleCardClick}
        className="group hover:scale-105 transition-transform"
      >
        <CardBody className="p-0">
          <div className="relative">
            <Image
              src={game.images.cover}
              alt={game.title}
              className="w-full h-32 object-cover"
              fallbackSrc="/api/placeholder/300/128"
            />
            <div className="absolute top-2 right-2">
              <Badge
                content={formatPlayerCount(game.playerCount)}
                color="primary"
                variant="solid"
                className="text-xs"
              >
                <UsersIcon className="w-4 h-4" />
              </Badge>
            </div>
            {showFavoriteButton && (
              <Button
                isIconOnly
                size="sm"
                variant="flat"
                className="absolute top-2 left-2 bg-black/50 backdrop-blur-sm"
                onClick={handleFavoriteClick}
              >
                {isFavorite ? (
                  <HeartSolidIcon className="w-4 h-4 text-red-500" />
                ) : (
                  <HeartIcon className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
          <div className="p-3">
            <h4 className="font-semibold text-sm mb-1 line-clamp-1">
              {game.title}
            </h4>
            <p className="text-xs text-default-500 mb-2">
              {game.developer}
            </p>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                {renderStars(game.rating).slice(0, 5)}
                <span className="text-xs text-default-500 ml-1">
                  {game.rating}
                </span>
              </div>
              <div className="flex flex-wrap gap-1">
                {game.genres.slice(0, 2).map((genre) => (
                  <Chip
                    key={genre}
                    size="sm"
                    variant="flat"
                    color="primary"
                    className="text-xs"
                  >
                    {genre}
                  </Chip>
                ))}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card
        isPressable={!!onGameClick}
        onPress={handleCardClick}
        className="group hover:scale-105 transition-transform"
      >
        <CardBody className="p-0">
          <div className="relative">
            <Image
              src={game.images.cover}
              alt={game.title}
              className="w-full h-40 object-cover"
              fallbackSrc="/api/placeholder/300/160"
            />
            <div className="absolute top-2 right-2">
              <Badge
                content={formatPlayerCount(game.playerCount)}
                color="primary"
                variant="solid"
                className="text-xs"
              >
                <UsersIcon className="w-4 h-4" />
              </Badge>
            </div>
            {showFavoriteButton && (
              <Button
                isIconOnly
                size="sm"
                variant="flat"
                className="absolute top-2 left-2 bg-black/50 backdrop-blur-sm"
                onClick={handleFavoriteClick}
              >
                {isFavorite ? (
                  <HeartSolidIcon className="w-4 h-4 text-red-500" />
                ) : (
                  <HeartIcon className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
          <div className="p-4">
            <h4 className="font-semibold mb-1 line-clamp-1">
              {game.title}
            </h4>
            <p className="text-sm text-default-500 mb-2">
              {game.developer}
            </p>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-1">
                {renderStars(game.rating).slice(0, 5)}
                <span className="text-sm text-default-500 ml-1">
                  {game.rating}
                </span>
              </div>
            </div>
            <div className="flex flex-wrap gap-1 mb-3">
              {game.genres.slice(0, 3).map((genre) => (
                <Chip
                  key={genre}
                  size="sm"
                  variant="flat"
                  color="primary"
                  className="text-xs"
                >
                  {genre}
                </Chip>
              ))}
            </div>
            <div className="flex flex-wrap gap-1">
              {game.platforms.slice(0, 3).map((platform) => (
                <Chip
                  key={platform}
                  size="sm"
                  variant="flat"
                  color="secondary"
                  className="text-xs"
                >
                  {platform}
                </Chip>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  // Default variant
  return (
    <Card
      isPressable={!!onGameClick}
      onPress={handleCardClick}
      className="group hover:scale-105 transition-transform"
    >
      <CardBody className="p-0">
        <div className="relative">
          <Image
            src={game.images.cover}
            alt={game.title}
            className="w-full h-36 object-cover"
            fallbackSrc="/api/placeholder/300/144"
          />
          <div className="absolute top-2 right-2">
            <Badge
              content={formatPlayerCount(game.playerCount)}
              color="primary"
              variant="solid"
              className="text-xs"
            >
              <UsersIcon className="w-4 h-4" />
            </Badge>
          </div>
          {showFavoriteButton && (
            <Button
              isIconOnly
              size="sm"
              variant="flat"
              className="absolute top-2 left-2 bg-black/50 backdrop-blur-sm"
              onClick={handleFavoriteClick}
            >
              {isFavorite ? (
                <HeartSolidIcon className="w-4 h-4 text-red-500" />
              ) : (
                <HeartIcon className="w-4 h-4" />
              )}
            </Button>
          )}
        </div>
        <div className="p-3">
          <h4 className="font-semibold text-sm mb-1 line-clamp-1">
            {game.title}
          </h4>
          <p className="text-xs text-default-500 mb-2">
            {game.developer}
          </p>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              {renderStars(game.rating).slice(0, 5)}
              <span className="text-xs text-default-500 ml-1">
                {game.rating}
              </span>
            </div>
            <div className="flex items-center gap-1 text-xs text-default-500">
              <UsersIcon className="w-3 h-3" />
              {formatPlayerCount(game.playerCount)}
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}

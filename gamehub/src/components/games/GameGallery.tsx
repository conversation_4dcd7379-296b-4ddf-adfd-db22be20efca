import { useState } from 'react';
import {
  Card,
  CardBody,
  Image,
  Button,
  Modal,
  ModalContent,
  ModalBody,
  useDisclosure,
} from '@heroui/react';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  PlayIcon,
} from '@heroicons/react/24/outline';

import { Game } from '@/types/database';

interface GameGalleryProps {
  game: Game;
}

interface MediaItem {
  type: 'image' | 'video';
  url: string;
  thumbnail?: string;
  title?: string;
}

export default function GameGallery({ game }: GameGalleryProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Create media items from game data
  const mediaItems: MediaItem[] = [
    // Main banner
    {
      type: 'image',
      url: game.images.banner,
      title: `${game.title} - Banner Principal`,
    },
    // Cover image
    {
      type: 'image',
      url: game.images.cover,
      title: `${game.title} - Capa`,
    },
    // Screenshots
    ...game.images.screenshots.map((screenshot, index) => ({
      type: 'image' as const,
      url: screenshot,
      title: `${game.title} - Screenshot ${index + 1}`,
    })),
    // Mock video content (in production, this would come from the game data)
    {
      type: 'video',
      url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Mock video URL
      thumbnail: game.images.banner,
      title: `${game.title} - Trailer Oficial`,
    },
  ];

  const openGallery = (index: number) => {
    setSelectedIndex(index);
    onOpen();
  };

  const navigateGallery = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setSelectedIndex(prev => prev === 0 ? mediaItems.length - 1 : prev - 1);
    } else {
      setSelectedIndex(prev => prev === mediaItems.length - 1 ? 0 : prev + 1);
    }
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isOpen) return;
    
    switch (event.key) {
      case 'ArrowLeft':
        navigateGallery('prev');
        break;
      case 'ArrowRight':
        navigateGallery('next');
        break;
      case 'Escape':
        onClose();
        break;
    }
  };

  // Add keyboard event listener
  useState(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  });

  return (
    <>
      <Card>
        <CardBody className="p-0">
          {/* Main Image */}
          <div className="relative group cursor-pointer" onClick={() => openGallery(0)}>
            <Image
              src={game.images.banner}
              alt={game.title}
              className="w-full h-64 md:h-80 object-cover"
              fallbackSrc="/api/placeholder/800/320"
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <Button
                isIconOnly
                variant="solid"
                color="primary"
                size="lg"
                className="bg-white/90 text-black hover:bg-white"
              >
                <PlayIcon className="w-6 h-6" />
              </Button>
            </div>
            <div className="absolute bottom-4 right-4">
              <Button
                size="sm"
                variant="solid"
                color="primary"
                className="bg-black/70 backdrop-blur-sm"
              >
                Ver Galeria ({mediaItems.length})
              </Button>
            </div>
          </div>

          {/* Thumbnail Grid */}
          <div className="p-4">
            <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
              {mediaItems.slice(1, 7).map((item, index) => (
                <div
                  key={index}
                  className="relative aspect-video cursor-pointer group"
                  onClick={() => openGallery(index + 1)}
                >
                  <Image
                    src={item.thumbnail || item.url}
                    alt={item.title || `${game.title} media ${index + 1}`}
                    className="w-full h-full object-cover rounded-lg"
                    fallbackSrc="/api/placeholder/150/85"
                  />
                  {item.type === 'video' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="bg-black/70 rounded-full p-2">
                        <PlayIcon className="w-4 h-4 text-white" />
                      </div>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 rounded-lg" />
                </div>
              ))}
              
              {/* Show more button if there are more items */}
              {mediaItems.length > 7 && (
                <div
                  className="relative aspect-video cursor-pointer group bg-default-100 rounded-lg flex items-center justify-center"
                  onClick={() => openGallery(7)}
                >
                  <div className="text-center">
                    <p className="text-sm font-semibold">+{mediaItems.length - 7}</p>
                    <p className="text-xs text-default-500">mais</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Gallery Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="full"
        hideCloseButton
        classNames={{
          base: "bg-black/95",
          backdrop: "bg-black/80",
        }}
      >
        <ModalContent>
          <ModalBody className="p-0 flex items-center justify-center">
            <div className="relative w-full h-full flex items-center justify-center">
              {/* Close Button */}
              <Button
                isIconOnly
                variant="flat"
                className="absolute top-4 right-4 z-10 bg-black/50 text-white"
                onClick={onClose}
              >
                <XMarkIcon className="w-6 h-6" />
              </Button>

              {/* Navigation Buttons */}
              <Button
                isIconOnly
                variant="flat"
                className="absolute left-4 z-10 bg-black/50 text-white"
                onClick={() => navigateGallery('prev')}
              >
                <ChevronLeftIcon className="w-6 h-6" />
              </Button>

              <Button
                isIconOnly
                variant="flat"
                className="absolute right-4 z-10 bg-black/50 text-white"
                onClick={() => navigateGallery('next')}
              >
                <ChevronRightIcon className="w-6 h-6" />
              </Button>

              {/* Media Content */}
              <div className="w-full h-full flex items-center justify-center p-8">
                {mediaItems[selectedIndex]?.type === 'video' ? (
                  <div className="w-full max-w-4xl aspect-video">
                    <iframe
                      src={mediaItems[selectedIndex].url}
                      title={mediaItems[selectedIndex].title}
                      className="w-full h-full rounded-lg"
                      allowFullScreen
                    />
                  </div>
                ) : (
                  <Image
                    src={mediaItems[selectedIndex]?.url}
                    alt={mediaItems[selectedIndex]?.title || `${game.title} media`}
                    className="max-w-full max-h-full object-contain"
                    fallbackSrc="/api/placeholder/800/600"
                  />
                )}
              </div>

              {/* Media Info */}
              <div className="absolute bottom-4 left-4 right-4 z-10">
                <div className="bg-black/70 backdrop-blur-sm rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-white font-semibold">
                        {mediaItems[selectedIndex]?.title}
                      </h4>
                      <p className="text-white/70 text-sm">
                        {selectedIndex + 1} de {mediaItems.length}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {mediaItems[selectedIndex]?.type === 'video' && (
                        <Button
                          size="sm"
                          color="primary"
                          variant="solid"
                        >
                          Assistir no YouTube
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Thumbnail Strip */}
              <div className="absolute bottom-20 left-4 right-4 z-10">
                <div className="flex gap-2 overflow-x-auto pb-2">
                  {mediaItems.map((item, index) => (
                    <div
                      key={index}
                      className={`relative flex-shrink-0 w-16 h-10 cursor-pointer rounded border-2 transition-colors ${
                        index === selectedIndex
                          ? 'border-primary'
                          : 'border-transparent hover:border-white/50'
                      }`}
                      onClick={() => setSelectedIndex(index)}
                    >
                      <Image
                        src={item.thumbnail || item.url}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover rounded"
                        fallbackSrc="/api/placeholder/64/40"
                      />
                      {item.type === 'video' && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <PlayIcon className="w-3 h-3 text-white" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}

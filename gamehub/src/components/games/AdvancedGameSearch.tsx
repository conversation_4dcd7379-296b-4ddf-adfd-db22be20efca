import { useState } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Input,
  Button,
  Select,
  SelectItem,
  Slider,
  Checkbox,
  CheckboxGroup,
  Chip,
  Divider,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from '@heroui/react';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';

import { popularGenres, popularPlatforms } from '@/data/mockGames';

interface AdvancedSearchFilters {
  searchTerm: string;
  genres: string[];
  platforms: string[];
  minRating: number;
  maxRating: number;
  minPlayerCount: number;
  maxPlayerCount: number;
  releaseDateRange: {
    start: string;
    end: string;
  };
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  tags: string[];
  developers: string[];
  isActive: boolean | null;
}

interface AdvancedGameSearchProps {
  onFiltersChange: (filters: AdvancedSearchFilters) => void;
  initialFilters?: Partial<AdvancedSearchFilters>;
}

const defaultFilters: AdvancedSearchFilters = {
  searchTerm: '',
  genres: [],
  platforms: [],
  minRating: 0,
  maxRating: 5,
  minPlayerCount: 0,
  maxPlayerCount: 1000000,
  releaseDateRange: {
    start: '2000-01-01',
    end: new Date().toISOString().split('T')[0],
  },
  sortBy: 'playerCount',
  sortOrder: 'desc',
  tags: [],
  developers: [],
  isActive: null,
};

const popularTags = [
  'Multiplayer', 'Singleplayer', 'Co-op', 'Competitive', 'Casual',
  'Hardcore', 'Story Rich', 'Open World', 'Indie', 'Early Access',
  'Free to Play', 'VR Support', 'Controller Support', 'Cross-platform'
];

const popularDevelopers = [
  'Valve Corporation', 'Riot Games', 'Epic Games', 'Blizzard Entertainment',
  'Ubisoft', 'Electronic Arts', 'Activision', 'Bethesda', 'CD Projekt RED',
  'Rockstar Games', 'Nintendo', 'Sony Interactive Entertainment'
];

export default function AdvancedGameSearch({
  onFiltersChange,
  initialFilters = {},
}: AdvancedGameSearchProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [filters, setFilters] = useState<AdvancedSearchFilters>({
    ...defaultFilters,
    ...initialFilters,
  });

  const [tempFilters, setTempFilters] = useState<AdvancedSearchFilters>(filters);

  const updateFilter = <K extends keyof AdvancedSearchFilters>(
    key: K,
    value: AdvancedSearchFilters[K]
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const updateTempFilter = <K extends keyof AdvancedSearchFilters>(
    key: K,
    value: AdvancedSearchFilters[K]
  ) => {
    setTempFilters(prev => ({ ...prev, [key]: value }));
  };

  const applyAdvancedFilters = () => {
    setFilters(tempFilters);
    onFiltersChange(tempFilters);
    onClose();
  };

  const resetFilters = () => {
    setFilters(defaultFilters);
    setTempFilters(defaultFilters);
    onFiltersChange(defaultFilters);
  };

  const clearFilter = (filterKey: keyof AdvancedSearchFilters) => {
    const clearedValue = Array.isArray(defaultFilters[filterKey])
      ? []
      : defaultFilters[filterKey];
    updateFilter(filterKey, clearedValue as any);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.genres.length > 0) count++;
    if (filters.platforms.length > 0) count++;
    if (filters.minRating > 0 || filters.maxRating < 5) count++;
    if (filters.minPlayerCount > 0 || filters.maxPlayerCount < 1000000) count++;
    if (filters.tags.length > 0) count++;
    if (filters.developers.length > 0) count++;
    if (filters.isActive !== null) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <>
      <Card>
        <CardBody className="space-y-4">
          {/* Basic Search */}
          <div className="flex gap-2">
            <Input
              placeholder="Buscar jogos, desenvolvedores, gêneros..."
              value={filters.searchTerm}
              onChange={(e) => updateFilter('searchTerm', e.target.value)}
              startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
              className="flex-1"
              size="lg"
            />
            <Button
              color="primary"
              variant="flat"
              startContent={<AdjustmentsHorizontalIcon className="w-4 h-4" />}
              onPress={onOpen}
            >
              Filtros Avançados
              {activeFiltersCount > 0 && (
                <Chip size="sm" color="primary" variant="solid" className="ml-2">
                  {activeFiltersCount}
                </Chip>
              )}
            </Button>
          </div>

          {/* Quick Filters */}
          <div className="flex flex-wrap gap-2">
            <Select
              placeholder="Gênero"
              selectedKeys={filters.genres}
              onSelectionChange={(keys) => updateFilter('genres', Array.from(keys) as string[])}
              selectionMode="multiple"
              className="w-48"
              size="sm"
            >
              {popularGenres.map((genre) => (
                <SelectItem key={genre} value={genre}>
                  {genre}
                </SelectItem>
              ))}
            </Select>

            <Select
              placeholder="Plataforma"
              selectedKeys={filters.platforms}
              onSelectionChange={(keys) => updateFilter('platforms', Array.from(keys) as string[])}
              selectionMode="multiple"
              className="w-48"
              size="sm"
            >
              {popularPlatforms.map((platform) => (
                <SelectItem key={platform} value={platform}>
                  {platform}
                </SelectItem>
              ))}
            </Select>

            <Select
              placeholder="Ordenar por"
              selectedKeys={[`${filters.sortBy}-${filters.sortOrder}`]}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                const [sortBy, sortOrder] = value.split('-');
                updateFilter('sortBy', sortBy);
                updateFilter('sortOrder', sortOrder as 'asc' | 'desc');
              }}
              className="w-48"
              size="sm"
            >
              <SelectItem key="playerCount-desc" value="playerCount-desc">
                Mais jogados
              </SelectItem>
              <SelectItem key="rating-desc" value="rating-desc">
                Melhor avaliados
              </SelectItem>
              <SelectItem key="title-asc" value="title-asc">
                Nome (A-Z)
              </SelectItem>
              <SelectItem key="releaseDate-desc" value="releaseDate-desc">
                Mais recentes
              </SelectItem>
            </Select>

            {activeFiltersCount > 0 && (
              <Button
                color="default"
                variant="flat"
                size="sm"
                onClick={resetFilters}
                startContent={<XMarkIcon className="w-4 h-4" />}
              >
                Limpar Filtros
              </Button>
            )}
          </div>

          {/* Active Filters Display */}
          {activeFiltersCount > 0 && (
            <div className="space-y-2">
              <p className="text-sm text-default-500">Filtros ativos:</p>
              <div className="flex flex-wrap gap-2">
                {filters.searchTerm && (
                  <Chip
                    onClose={() => clearFilter('searchTerm')}
                    variant="flat"
                    color="primary"
                  >
                    Busca: "{filters.searchTerm}"
                  </Chip>
                )}
                {filters.genres.map((genre) => (
                  <Chip
                    key={genre}
                    onClose={() => updateFilter('genres', filters.genres.filter(g => g !== genre))}
                    variant="flat"
                    color="secondary"
                  >
                    {genre}
                  </Chip>
                ))}
                {filters.platforms.map((platform) => (
                  <Chip
                    key={platform}
                    onClose={() => updateFilter('platforms', filters.platforms.filter(p => p !== platform))}
                    variant="flat"
                    color="success"
                  >
                    {platform}
                  </Chip>
                ))}
                {(filters.minRating > 0 || filters.maxRating < 5) && (
                  <Chip
                    onClose={() => {
                      updateFilter('minRating', 0);
                      updateFilter('maxRating', 5);
                    }}
                    variant="flat"
                    color="warning"
                  >
                    Rating: {filters.minRating}-{filters.maxRating}★
                  </Chip>
                )}
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Advanced Filters Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="4xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <FunnelIcon className="w-5 h-5" />
              <span>Filtros Avançados</span>
            </div>
          </ModalHeader>
          <ModalBody className="space-y-6">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Busca por Texto
              </label>
              <Input
                placeholder="Buscar em títulos, desenvolvedores, descrições..."
                value={tempFilters.searchTerm}
                onChange={(e) => updateTempFilter('searchTerm', e.target.value)}
                startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
              />
            </div>

            {/* Genres */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Gêneros
              </label>
              <CheckboxGroup
                value={tempFilters.genres}
                onValueChange={(value) => updateTempFilter('genres', value)}
                orientation="horizontal"
                className="gap-2"
              >
                <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                  {popularGenres.map((genre) => (
                    <Checkbox key={genre} value={genre} size="sm">
                      {genre}
                    </Checkbox>
                  ))}
                </div>
              </CheckboxGroup>
            </div>

            {/* Platforms */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Plataformas
              </label>
              <CheckboxGroup
                value={tempFilters.platforms}
                onValueChange={(value) => updateTempFilter('platforms', value)}
                orientation="horizontal"
                className="gap-2"
              >
                <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                  {popularPlatforms.map((platform) => (
                    <Checkbox key={platform} value={platform} size="sm">
                      {platform}
                    </Checkbox>
                  ))}
                </div>
              </CheckboxGroup>
            </div>

            <Divider />

            {/* Rating Range */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Faixa de Avaliação: {tempFilters.minRating}★ - {tempFilters.maxRating}★
              </label>
              <div className="space-y-4">
                <Slider
                  label="Avaliação Mínima"
                  step={0.5}
                  maxValue={5}
                  minValue={0}
                  value={tempFilters.minRating}
                  onChange={(value) => updateTempFilter('minRating', value as number)}
                  className="max-w-md"
                />
                <Slider
                  label="Avaliação Máxima"
                  step={0.5}
                  maxValue={5}
                  minValue={0}
                  value={tempFilters.maxRating}
                  onChange={(value) => updateTempFilter('maxRating', value as number)}
                  className="max-w-md"
                />
              </div>
            </div>

            {/* Player Count Range */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Número de Jogadores Ativos
              </label>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    label="Mínimo"
                    value={tempFilters.minPlayerCount.toString()}
                    onChange={(e) => updateTempFilter('minPlayerCount', parseInt(e.target.value) || 0)}
                  />
                  <Input
                    type="number"
                    label="Máximo"
                    value={tempFilters.maxPlayerCount.toString()}
                    onChange={(e) => updateTempFilter('maxPlayerCount', parseInt(e.target.value) || 1000000)}
                  />
                </div>
              </div>
            </div>

            {/* Release Date Range */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Período de Lançamento
              </label>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="date"
                  label="Data Inicial"
                  value={tempFilters.releaseDateRange.start}
                  onChange={(e) => updateTempFilter('releaseDateRange', {
                    ...tempFilters.releaseDateRange,
                    start: e.target.value
                  })}
                />
                <Input
                  type="date"
                  label="Data Final"
                  value={tempFilters.releaseDateRange.end}
                  onChange={(e) => updateTempFilter('releaseDateRange', {
                    ...tempFilters.releaseDateRange,
                    end: e.target.value
                  })}
                />
              </div>
            </div>

            <Divider />

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Tags
              </label>
              <CheckboxGroup
                value={tempFilters.tags}
                onValueChange={(value) => updateTempFilter('tags', value)}
                orientation="horizontal"
                className="gap-2"
              >
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {popularTags.map((tag) => (
                    <Checkbox key={tag} value={tag} size="sm">
                      {tag}
                    </Checkbox>
                  ))}
                </div>
              </CheckboxGroup>
            </div>

            {/* Developers */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Desenvolvedores
              </label>
              <CheckboxGroup
                value={tempFilters.developers}
                onValueChange={(value) => updateTempFilter('developers', value)}
                orientation="horizontal"
                className="gap-2"
              >
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {popularDevelopers.map((dev) => (
                    <Checkbox key={dev} value={dev} size="sm">
                      {dev}
                    </Checkbox>
                  ))}
                </div>
              </CheckboxGroup>
            </div>

            {/* Game Status */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Status do Jogo
              </label>
              <div className="flex gap-2">
                <Chip
                  variant={tempFilters.isActive === true ? 'solid' : 'flat'}
                  color="success"
                  className="cursor-pointer"
                  onClick={() => updateTempFilter('isActive', tempFilters.isActive === true ? null : true)}
                >
                  Apenas Ativos
                </Chip>
                <Chip
                  variant={tempFilters.isActive === false ? 'solid' : 'flat'}
                  color="danger"
                  className="cursor-pointer"
                  onClick={() => updateTempFilter('isActive', tempFilters.isActive === false ? null : false)}
                >
                  Incluir Inativos
                </Chip>
                <Chip
                  variant={tempFilters.isActive === null ? 'solid' : 'flat'}
                  color="default"
                  className="cursor-pointer"
                  onClick={() => updateTempFilter('isActive', null)}
                >
                  Todos
                </Chip>
              </div>
            </div>

            {/* Sort Options */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Ordenação
              </label>
              <div className="grid grid-cols-2 gap-4">
                <Select
                  label="Ordenar por"
                  selectedKeys={[tempFilters.sortBy]}
                  onSelectionChange={(keys) => updateTempFilter('sortBy', Array.from(keys)[0] as string)}
                >
                  <SelectItem key="playerCount" value="playerCount">
                    Número de Jogadores
                  </SelectItem>
                  <SelectItem key="rating" value="rating">
                    Avaliação
                  </SelectItem>
                  <SelectItem key="title" value="title">
                    Nome
                  </SelectItem>
                  <SelectItem key="releaseDate" value="releaseDate">
                    Data de Lançamento
                  </SelectItem>
                </Select>
                <Select
                  label="Ordem"
                  selectedKeys={[tempFilters.sortOrder]}
                  onSelectionChange={(keys) => updateTempFilter('sortOrder', Array.from(keys)[0] as 'asc' | 'desc')}
                >
                  <SelectItem key="desc" value="desc">
                    Decrescente
                  </SelectItem>
                  <SelectItem key="asc" value="asc">
                    Crescente
                  </SelectItem>
                </Select>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="flat"
              onClick={() => {
                setTempFilters(defaultFilters);
              }}
            >
              Limpar Tudo
            </Button>
            <Button variant="flat" onPress={onClose}>
              Cancelar
            </Button>
            <Button color="primary" onPress={applyAdvancedFilters}>
              Aplicar Filtros
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}

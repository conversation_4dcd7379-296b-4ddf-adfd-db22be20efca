import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Divider,
  Image,
  Tabs,
  Tab,
} from '@heroui/react';
import {
  HeartIcon,
  UsersIcon,
  StarIcon,
  EyeIcon,
  PlayIcon,
  PhotoIcon,
  ChatBubbleLeftRightIcon,
  PuzzlePieceIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartSolidIcon,
  StarIcon as StarSolidIcon,
} from '@heroicons/react/24/solid';

import { Game } from '@/types/database';
import GameGallery from './GameGallery';
import GameReviews from './GameReviews';
import RelatedGames, { MoreLikeThis } from './RelatedGames';

interface GameDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  game: Game | null;
  isFavorite: boolean;
  onToggleFavorite: (gameId: string) => void;
  onGenreClick?: (genre: string) => void;
  onPlatformClick?: (platform: string) => void;
}

export default function GameDetailsModal({
  isOpen,
  onClose,
  game,
  isFavorite,
  onToggleFavorite,
  onGenreClick,
  onPlatformClick,
}: GameDetailsModalProps) {
  if (!game) return null;

  const formatPlayerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(0)}K`;
    }
    return count.toString();
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <StarSolidIcon key={i} className="w-4 h-4 text-yellow-500" />
      );
    }
    
    if (hasHalfStar) {
      stars.push(
        <StarIcon key="half" className="w-4 h-4 text-yellow-500" />
      );
    }
    
    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <StarIcon key={`empty-${i}`} className="w-4 h-4 text-gray-400" />
      );
    }
    
    return stars;
  };

  const handleGenreClick = (genre: string) => {
    if (onGenreClick) {
      onGenreClick(genre);
      onClose();
    }
  };

  const handlePlatformClick = (platform: string) => {
    if (onPlatformClick) {
      onPlatformClick(platform);
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "p-0",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1 p-6 pb-0">
          <div className="flex items-start justify-between">
            <div>
              <h2 className="text-2xl font-bold">{game.title}</h2>
              <p className="text-default-500">{game.developer}</p>
            </div>
            <Button
              isIconOnly
              variant="flat"
              onClick={() => onToggleFavorite(game.id)}
            >
              {isFavorite ? (
                <HeartSolidIcon className="w-5 h-5 text-red-500" />
              ) : (
                <HeartIcon className="w-5 h-5" />
              )}
            </Button>
          </div>
        </ModalHeader>
        
        <ModalBody>
          <div className="p-6 pt-4">
            {/* Quick Info Bar */}
            <div className="flex items-center justify-between mb-6 p-4 bg-default-100 rounded-lg">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  {renderStars(game.rating)}
                  <span className="ml-2 font-semibold">
                    {game.rating}/5
                  </span>
                </div>
                <Divider orientation="vertical" className="h-6" />
                <div className="flex items-center gap-1">
                  <UsersIcon className="w-4 h-4" />
                  <span>{formatPlayerCount(game.playerCount)} jogadores</span>
                </div>
                <Divider orientation="vertical" className="h-6" />
                <div className="flex flex-wrap gap-1">
                  {game.genres.slice(0, 2).map((genre) => (
                    <Chip
                      key={genre}
                      size="sm"
                      color="primary"
                      variant="flat"
                    >
                      {genre}
                    </Chip>
                  ))}
                </div>
              </div>
              <div className="flex gap-2">
                {Object.keys(game.externalIds).length > 0 && (
                  <>
                    {game.externalIds.steam && (
                      <Button
                        as="a"
                        href={`https://store.steampowered.com/app/${game.externalIds.steam}`}
                        target="_blank"
                        variant="flat"
                        color="primary"
                        size="sm"
                      >
                        Steam
                      </Button>
                    )}
                    {game.externalIds.epic && (
                      <Button
                        as="a"
                        href={`https://store.epicgames.com/p/${game.externalIds.epic}`}
                        target="_blank"
                        variant="flat"
                        color="secondary"
                        size="sm"
                      >
                        Epic Games
                      </Button>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Tabs */}
            <Tabs
              variant="underlined"
              classNames={{
                tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                cursor: "w-full bg-primary",
                tab: "max-w-fit px-0 h-12",
              }}
            >
              <Tab
                key="overview"
                title={
                  <div className="flex items-center gap-2">
                    <EyeIcon className="w-4 h-4" />
                    <span>Visão Geral</span>
                  </div>
                }
              >
                <div className="space-y-6 py-4">
                  {/* Game Info Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Main Info */}
                    <div className="md:col-span-2 space-y-4">
                      <div>
                        <h3 className="text-lg font-semibold mb-2">Descrição</h3>
                        <p className="text-default-600 leading-relaxed">
                          {game.description}
                        </p>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold mb-3">Gêneros</h3>
                        <div className="flex flex-wrap gap-2">
                          {game.genres.map((genre) => (
                            <Chip
                              key={genre}
                              color="primary"
                              variant="flat"
                              className="cursor-pointer hover:bg-primary/20"
                              onClick={() => handleGenreClick(genre)}
                            >
                              {genre}
                            </Chip>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold mb-3">Plataformas</h3>
                        <div className="flex flex-wrap gap-2">
                          {game.platforms.map((platform) => (
                            <Chip
                              key={platform}
                              color="secondary"
                              variant="flat"
                              className="cursor-pointer hover:bg-secondary/20"
                              onClick={() => handlePlatformClick(platform)}
                            >
                              {platform}
                            </Chip>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold mb-3">Tags</h3>
                        <div className="flex flex-wrap gap-2">
                          {game.tags.map((tag) => (
                            <Chip
                              key={tag}
                              color="default"
                              variant="flat"
                              size="sm"
                            >
                              {tag}
                            </Chip>
                          ))}
                        </div>
                      </div>

                      {/* More Like This */}
                      <MoreLikeThis
                        currentGame={game}
                        onGameClick={onClose} // Close modal when clicking another game
                        userFavorites={[]}
                        showFavoriteButton={false}
                      />
                    </div>

                    {/* Side Info */}
                    <div className="space-y-4">
                      <Card>
                        <CardBody className="space-y-3">
                          <div>
                            <p className="text-sm text-default-500">Desenvolvedor</p>
                            <p className="font-semibold">{game.developer}</p>
                          </div>
                          <Divider />
                          <div>
                            <p className="text-sm text-default-500">Publicadora</p>
                            <p className="font-semibold">{game.publisher}</p>
                          </div>
                          <Divider />
                          <div>
                            <p className="text-sm text-default-500">Data de Lançamento</p>
                            <p className="font-semibold">
                              {game.releaseDate.toDate().toLocaleDateString('pt-BR')}
                            </p>
                          </div>
                          <Divider />
                          <div>
                            <p className="text-sm text-default-500">Jogadores Ativos</p>
                            <p className="font-semibold text-success">
                              {formatPlayerCount(game.playerCount)}
                            </p>
                          </div>
                        </CardBody>
                      </Card>
                    </div>
                  </div>
                </div>
              </Tab>

              <Tab
                key="gallery"
                title={
                  <div className="flex items-center gap-2">
                    <PhotoIcon className="w-4 h-4" />
                    <span>Galeria</span>
                  </div>
                }
              >
                <div className="py-4">
                  <GameGallery game={game} />
                </div>
              </Tab>

              <Tab
                key="reviews"
                title={
                  <div className="flex items-center gap-2">
                    <ChatBubbleLeftRightIcon className="w-4 h-4" />
                    <span>Avaliações</span>
                  </div>
                }
              >
                <div className="py-4">
                  <GameReviews gameId={game.id} gameTitle={game.title} />
                </div>
              </Tab>

              <Tab
                key="related"
                title={
                  <div className="flex items-center gap-2">
                    <PuzzlePieceIcon className="w-4 h-4" />
                    <span>Relacionados</span>
                  </div>
                }
              >
                <div className="py-4">
                  <RelatedGames
                    currentGame={game}
                    onGameClick={onClose} // Close modal when clicking another game
                    userFavorites={[]}
                    showFavoriteButton={false}
                  />
                </div>
              </Tab>
            </Tabs>
          </div>
        </ModalBody>
        
        <ModalFooter className="justify-between">
          <Button variant="flat" onPress={onClose}>
            Fechar
          </Button>
          <div className="flex gap-2">
            <Button
              color="primary"
              variant="flat"
              startContent={<EyeIcon className="w-4 h-4" />}
            >
              Ver Comunidade
            </Button>
            <Button
              color="primary"
              startContent={<PlayIcon className="w-4 h-4" />}
            >
              Jogar Agora
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}

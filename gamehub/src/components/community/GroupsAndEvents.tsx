import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Badge,
  Input,
  Textarea,
  Select,
  SelectItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Tabs,
  Tab,
} from '@heroui/react';
import {
  UserGroupIcon,
  CalendarDaysIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  ClockIcon,
  UsersIcon,
  StarIcon,
  GlobeAltIcon,
  LockClosedIcon,
} from '@heroicons/react/24/outline';

import { useAuth } from '@/contexts/AuthContext';
import { Group, Event } from '@/types/community';

interface GroupsAndEventsProps {
  initialTab?: 'groups' | 'events';
}

// Mock data for demonstration
const mockGroups: Group[] = [
  {
    id: '1',
    name: 'Souls-like Brasil',
    description: 'Comunidade brasileira dedicada aos jogos souls-like. Compartilhamos dicas, builds e ajudamos uns aos outros a superar os desafios.',
    avatar: 'https://i.pravatar.cc/150?u=group1',
    banner: '/api/placeholder/400/200',
    category: 'Gaming',
    gameId: 'dark-souls',
    gameName: 'Dark Souls',
    creatorId: 'user1',
    moderatorIds: ['user1', 'user2'],
    memberIds: ['user1', 'user2', 'user3'],
    membersCount: 1247,
    postsCount: 89,
    isPrivate: false,
    requiresApproval: false,
    rules: [
      'Seja respeitoso com todos os membros',
      'Sem spoilers sem aviso prévio',
      'Ajude outros jogadores quando possível',
      'Mantenha discussões relacionadas ao tema'
    ],
    tags: ['souls-like', 'dark-souls', 'elden-ring', 'bloodborne'],
    createdAt: { seconds: Date.now() / 1000 - 86400 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 3600 } as any,
    lastActivity: { seconds: Date.now() / 1000 - 1800 } as any,
  },
  {
    id: '2',
    name: 'Valorant Competitivo',
    description: 'Para jogadores que levam Valorant a sério. Discussões sobre meta, estratégias e formação de times.',
    avatar: 'https://i.pravatar.cc/150?u=group2',
    banner: '/api/placeholder/400/200',
    category: 'Competitive',
    gameId: 'valorant',
    gameName: 'Valorant',
    creatorId: 'user2',
    moderatorIds: ['user2'],
    memberIds: ['user1', 'user2', 'user3', 'user4'],
    membersCount: 892,
    postsCount: 156,
    isPrivate: false,
    requiresApproval: true,
    rules: [
      'Rank mínimo: Ouro',
      'Sem toxicidade ou comportamento tóxico',
      'Compartilhe estratégias construtivas',
      'Respeite diferentes estilos de jogo'
    ],
    tags: ['valorant', 'competitive', 'fps', 'esports'],
    createdAt: { seconds: Date.now() / 1000 - 172800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 7200 } as any,
    lastActivity: { seconds: Date.now() / 1000 - 3600 } as any,
  },
];

const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Torneio de Valorant - Copa GameHub',
    description: 'Torneio competitivo de Valorant com premiação em dinheiro. Inscrições abertas para times de 5 jogadores.',
    type: 'tournament',
    gameId: 'valorant',
    gameName: 'Valorant',
    organizerId: 'user1',
    organizerName: 'GamerPro2024',
    groupId: '2',
    groupName: 'Valorant Competitivo',
    startDate: { seconds: (Date.now() + 86400000) / 1000 } as any,
    endDate: { seconds: (Date.now() + 172800000) / 1000 } as any,
    timezone: 'America/Sao_Paulo',
    onlineLink: 'https://discord.gg/gamehub-tournament',
    maxParticipants: 64,
    participantIds: ['user1', 'user2'],
    participantsCount: 32,
    interestedIds: ['user3', 'user4'],
    interestedCount: 89,
    tags: ['valorant', 'tournament', 'competitive', 'prize'],
    isPublic: true,
    status: 'upcoming',
    createdAt: { seconds: Date.now() / 1000 - 86400 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 3600 } as any,
  },
  {
    id: '2',
    title: 'Meetup Gamer São Paulo',
    description: 'Encontro presencial de gamers em São Paulo. Vamos nos conhecer, jogar alguns jogos locais e trocar experiências.',
    type: 'meetup',
    organizerId: 'user2',
    organizerName: 'StreamerQueen',
    startDate: { seconds: (Date.now() + 259200000) / 1000 } as any,
    endDate: { seconds: (Date.now() + 273600000) / 1000 } as any,
    timezone: 'America/Sao_Paulo',
    location: 'Shopping Eldorado - São Paulo, SP',
    maxParticipants: 30,
    participantIds: ['user1', 'user2', 'user3'],
    participantsCount: 15,
    interestedIds: ['user4', 'user5'],
    interestedCount: 25,
    tags: ['meetup', 'sao-paulo', 'presencial', 'networking'],
    isPublic: true,
    status: 'upcoming',
    createdAt: { seconds: Date.now() / 1000 - 172800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 7200 } as any,
  },
];

export default function GroupsAndEvents({ initialTab = 'groups' }: GroupsAndEventsProps) {
  const { currentUser } = useAuth();
  const { isOpen: isGroupModalOpen, onOpen: onGroupModalOpen, onClose: onGroupModalClose } = useDisclosure();
  const { isOpen: isEventModalOpen, onOpen: onEventModalOpen, onClose: onEventModalClose } = useDisclosure();
  
  const [selectedTab, setSelectedTab] = useState(initialTab);
  const [groups, setGroups] = useState<Group[]>(mockGroups);
  const [events, setEvents] = useState<Event[]>(mockEvents);
  const [searchTerm, setSearchTerm] = useState('');
  const [joinedGroups, setJoinedGroups] = useState<string[]>(['1']);
  const [interestedEvents, setInterestedEvents] = useState<string[]>([]);
  const [participatingEvents, setParticipatingEvents] = useState<string[]>([]);

  // New group form
  const [newGroup, setNewGroup] = useState({
    name: '',
    description: '',
    category: '',
    gameId: '',
    isPrivate: false,
    requiresApproval: false,
    rules: '',
    tags: '',
  });

  // New event form
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    type: 'meetup',
    gameId: '',
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
    location: '',
    onlineLink: '',
    maxParticipants: '',
    tags: '',
  });

  const handleJoinGroup = (groupId: string) => {
    if (joinedGroups.includes(groupId)) {
      setJoinedGroups(prev => prev.filter(id => id !== groupId));
      setGroups(prev => prev.map(group => 
        group.id === groupId 
          ? { ...group, membersCount: group.membersCount - 1 }
          : group
      ));
    } else {
      setJoinedGroups(prev => [...prev, groupId]);
      setGroups(prev => prev.map(group => 
        group.id === groupId 
          ? { ...group, membersCount: group.membersCount + 1 }
          : group
      ));
    }
  };

  const handleEventInterest = (eventId: string) => {
    if (interestedEvents.includes(eventId)) {
      setInterestedEvents(prev => prev.filter(id => id !== eventId));
      setEvents(prev => prev.map(event => 
        event.id === eventId 
          ? { ...event, interestedCount: event.interestedCount - 1 }
          : event
      ));
    } else {
      setInterestedEvents(prev => [...prev, eventId]);
      setEvents(prev => prev.map(event => 
        event.id === eventId 
          ? { ...event, interestedCount: event.interestedCount + 1 }
          : event
      ));
    }
  };

  const handleEventParticipation = (eventId: string) => {
    if (participatingEvents.includes(eventId)) {
      setParticipatingEvents(prev => prev.filter(id => id !== eventId));
      setEvents(prev => prev.map(event => 
        event.id === eventId 
          ? { ...event, participantsCount: event.participantsCount - 1 }
          : event
      ));
    } else {
      setParticipatingEvents(prev => [...prev, eventId]);
      setEvents(prev => prev.map(event => 
        event.id === eventId 
          ? { ...event, participantsCount: event.participantsCount + 1 }
          : event
      ));
    }
  };

  const formatEventDate = (timestamp: any) => {
    return new Date(timestamp.seconds * 1000).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  const formatEventTime = (timestamp: any) => {
    return new Date(timestamp.seconds * 1000).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Grupos e Eventos</h2>
          <p className="text-default-600">Conecte-se com outros gamers e participe de eventos incríveis</p>
        </div>
      </div>

      {/* Search */}
      <Input
        placeholder="Buscar grupos e eventos..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
        className="max-w-md"
      />

      {/* Tabs */}
      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={(key) => setSelectedTab(key as string)}
        variant="underlined"
        classNames={{
          tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
          cursor: "w-full bg-primary",
          tab: "max-w-fit px-0 h-12",
        }}
      >
        <Tab
          key="groups"
          title={
            <div className="flex items-center gap-2">
              <UserGroupIcon className="w-4 h-4" />
              <span>Grupos</span>
            </div>
          }
        >
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Grupos da Comunidade</h3>
              {currentUser && (
                <Button
                  color="primary"
                  startContent={<PlusIcon className="w-4 h-4" />}
                  onClick={onGroupModalOpen}
                >
                  Criar Grupo
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredGroups.map((group) => (
                <Card key={group.id} className="h-full">
                  <CardBody className="p-0">
                    {/* Group Banner */}
                    <div className="relative h-32 bg-gradient-to-r from-primary to-secondary">
                      <div className="absolute inset-0 bg-black/20" />
                      <div className="absolute top-3 right-3">
                        {group.isPrivate ? (
                          <Chip size="sm" color="warning" variant="solid">
                            <LockClosedIcon className="w-3 h-3 mr-1" />
                            Privado
                          </Chip>
                        ) : (
                          <Chip size="sm" color="success" variant="solid">
                            <GlobeAltIcon className="w-3 h-3 mr-1" />
                            Público
                          </Chip>
                        )}
                      </div>
                    </div>

                    {/* Group Info */}
                    <div className="p-4 -mt-8 relative">
                      <Avatar
                        src={group.avatar}
                        name={group.name}
                        size="lg"
                        className="border-4 border-background mb-3"
                      />
                      
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-lg">{group.name}</h4>
                          <p className="text-sm text-default-500 line-clamp-2">
                            {group.description}
                          </p>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-default-500">
                          <div className="flex items-center gap-1">
                            <UsersIcon className="w-4 h-4" />
                            <span>{group.membersCount.toLocaleString()}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <StarIcon className="w-4 h-4" />
                            <span>{group.postsCount}</span>
                          </div>
                        </div>

                        {group.gameName && (
                          <Chip size="sm" color="primary" variant="flat">
                            {group.gameName}
                          </Chip>
                        )}

                        <div className="flex flex-wrap gap-1">
                          {group.tags.slice(0, 3).map((tag) => (
                            <Chip
                              key={tag}
                              size="sm"
                              variant="flat"
                              color="secondary"
                              className="text-xs"
                            >
                              #{tag}
                            </Chip>
                          ))}
                        </div>

                        <Button
                          color={joinedGroups.includes(group.id) ? "default" : "primary"}
                          variant={joinedGroups.includes(group.id) ? "flat" : "solid"}
                          size="sm"
                          className="w-full"
                          onClick={() => handleJoinGroup(group.id)}
                        >
                          {joinedGroups.includes(group.id) ? 'Sair do Grupo' : 'Entrar no Grupo'}
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        </Tab>

        <Tab
          key="events"
          title={
            <div className="flex items-center gap-2">
              <CalendarDaysIcon className="w-4 h-4" />
              <span>Eventos</span>
            </div>
          }
        >
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Próximos Eventos</h3>
              {currentUser && (
                <Button
                  color="primary"
                  startContent={<PlusIcon className="w-4 h-4" />}
                  onClick={onEventModalOpen}
                >
                  Criar Evento
                </Button>
              )}
            </div>

            <div className="space-y-4">
              {filteredEvents.map((event) => (
                <Card key={event.id} className="w-full">
                  <CardBody className="p-6">
                    <div className="flex items-start gap-4">
                      {/* Event Date */}
                      <div className="flex-shrink-0">
                        <div className="w-16 h-16 bg-primary/10 rounded-lg flex flex-col items-center justify-center">
                          <span className="text-xs text-primary font-medium">
                            {new Date(event.startDate.seconds * 1000).getDate()}
                          </span>
                          <span className="text-xs text-primary">
                            {new Date(event.startDate.seconds * 1000).toLocaleDateString('pt-BR', { month: 'short' })}
                          </span>
                        </div>
                      </div>

                      {/* Event Info */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h4 className="font-semibold text-lg">{event.title}</h4>
                            <p className="text-sm text-default-500">
                              Por {event.organizerName}
                              {event.groupName && (
                                <span> • Grupo: {event.groupName}</span>
                              )}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Chip
                              size="sm"
                              color="primary"
                              variant="flat"
                            >
                              {event.type}
                            </Chip>
                            <Chip
                              size="sm"
                              color={event.status === 'upcoming' ? 'success' : 'default'}
                              variant="flat"
                            >
                              {event.status === 'upcoming' ? 'Em breve' : event.status}
                            </Chip>
                          </div>
                        </div>

                        <p className="text-default-600 mb-3 line-clamp-2">
                          {event.description}
                        </p>

                        {/* Event Details */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4 text-sm">
                          <div className="flex items-center gap-2 text-default-500">
                            <CalendarDaysIcon className="w-4 h-4" />
                            <span>
                              {formatEventDate(event.startDate)} - {formatEventDate(event.endDate)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-default-500">
                            <ClockIcon className="w-4 h-4" />
                            <span>
                              {formatEventTime(event.startDate)} - {formatEventTime(event.endDate)}
                            </span>
                          </div>
                          {event.location && (
                            <div className="flex items-center gap-2 text-default-500">
                              <MapPinIcon className="w-4 h-4" />
                              <span>{event.location}</span>
                            </div>
                          )}
                          {event.gameName && (
                            <div className="flex items-center gap-2">
                              <Chip size="sm" color="secondary" variant="flat">
                                {event.gameName}
                              </Chip>
                            </div>
                          )}
                        </div>

                        {/* Event Tags */}
                        {event.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-4">
                            {event.tags.map((tag) => (
                              <Chip
                                key={tag}
                                size="sm"
                                variant="flat"
                                color="default"
                                className="text-xs"
                              >
                                #{tag}
                              </Chip>
                            ))}
                          </div>
                        )}

                        {/* Event Stats and Actions */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm">
                            <span className="text-success">
                              ✅ {event.participantsCount} participando
                            </span>
                            <span className="text-warning">
                              ⭐ {event.interestedCount} interessados
                            </span>
                            {event.maxParticipants && (
                              <span className="text-default-500">
                                📊 {event.maxParticipants} vagas
                              </span>
                            )}
                          </div>

                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="flat"
                              color={interestedEvents.includes(event.id) ? "warning" : "default"}
                              onClick={() => handleEventInterest(event.id)}
                            >
                              {interestedEvents.includes(event.id) ? '⭐ Interessado' : 'Interessar-se'}
                            </Button>
                            <Button
                              size="sm"
                              color={participatingEvents.includes(event.id) ? "success" : "primary"}
                              variant={participatingEvents.includes(event.id) ? "flat" : "solid"}
                              onClick={() => handleEventParticipation(event.id)}
                            >
                              {participatingEvents.includes(event.id) ? '✅ Participando' : 'Participar'}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        </Tab>
      </Tabs>

      {/* Create Group Modal */}
      <Modal
        isOpen={isGroupModalOpen}
        onClose={onGroupModalClose}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <h3>Criar Novo Grupo</h3>
          </ModalHeader>
          <ModalBody className="space-y-4">
            <Input
              label="Nome do Grupo"
              placeholder="Digite um nome atrativo para seu grupo"
              value={newGroup.name}
              onChange={(e) => setNewGroup(prev => ({ ...prev, name: e.target.value }))}
              maxLength={50}
            />

            <Textarea
              label="Descrição"
              placeholder="Descreva o propósito e objetivos do grupo"
              value={newGroup.description}
              onChange={(e) => setNewGroup(prev => ({ ...prev, description: e.target.value }))}
              minRows={3}
              maxRows={6}
              maxLength={500}
            />

            <Select
              label="Categoria"
              placeholder="Selecione uma categoria"
              value={newGroup.category}
              onChange={(e) => setNewGroup(prev => ({ ...prev, category: e.target.value }))}
            >
              <SelectItem key="gaming" value="gaming">Gaming</SelectItem>
              <SelectItem key="competitive" value="competitive">Competitivo</SelectItem>
              <SelectItem key="casual" value="casual">Casual</SelectItem>
              <SelectItem key="social" value="social">Social</SelectItem>
              <SelectItem key="educational" value="educational">Educacional</SelectItem>
            </Select>

            <Input
              label="Jogo Principal (opcional)"
              placeholder="Ex: Valorant, League of Legends"
              value={newGroup.gameId}
              onChange={(e) => setNewGroup(prev => ({ ...prev, gameId: e.target.value }))}
            />

            <Textarea
              label="Regras do Grupo"
              placeholder="Liste as regras do grupo, uma por linha"
              value={newGroup.rules}
              onChange={(e) => setNewGroup(prev => ({ ...prev, rules: e.target.value }))}
              minRows={3}
              maxRows={6}
            />

            <Input
              label="Tags"
              placeholder="Separe as tags por vírgula: fps, competitivo, iniciante"
              value={newGroup.tags}
              onChange={(e) => setNewGroup(prev => ({ ...prev, tags: e.target.value }))}
            />
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onGroupModalClose}>
              Cancelar
            </Button>
            <Button
              color="primary"
              isDisabled={!newGroup.name.trim() || !newGroup.description.trim()}
            >
              Criar Grupo
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Create Event Modal */}
      <Modal
        isOpen={isEventModalOpen}
        onClose={onEventModalClose}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <h3>Criar Novo Evento</h3>
          </ModalHeader>
          <ModalBody className="space-y-4">
            <Input
              label="Título do Evento"
              placeholder="Digite um título atrativo para seu evento"
              value={newEvent.title}
              onChange={(e) => setNewEvent(prev => ({ ...prev, title: e.target.value }))}
              maxLength={100}
            />

            <Textarea
              label="Descrição"
              placeholder="Descreva o evento em detalhes"
              value={newEvent.description}
              onChange={(e) => setNewEvent(prev => ({ ...prev, description: e.target.value }))}
              minRows={3}
              maxRows={6}
              maxLength={1000}
            />

            <Select
              label="Tipo de Evento"
              value={newEvent.type}
              onChange={(e) => setNewEvent(prev => ({ ...prev, type: e.target.value }))}
            >
              <SelectItem key="tournament" value="tournament">Torneio</SelectItem>
              <SelectItem key="meetup" value="meetup">Encontro</SelectItem>
              <SelectItem key="stream" value="stream">Stream</SelectItem>
              <SelectItem key="discussion" value="discussion">Discussão</SelectItem>
              <SelectItem key="workshop" value="workshop">Workshop</SelectItem>
            </Select>

            <div className="grid grid-cols-2 gap-4">
              <Input
                type="date"
                label="Data de Início"
                value={newEvent.startDate}
                onChange={(e) => setNewEvent(prev => ({ ...prev, startDate: e.target.value }))}
              />
              <Input
                type="time"
                label="Hora de Início"
                value={newEvent.startTime}
                onChange={(e) => setNewEvent(prev => ({ ...prev, startTime: e.target.value }))}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                type="date"
                label="Data de Término"
                value={newEvent.endDate}
                onChange={(e) => setNewEvent(prev => ({ ...prev, endDate: e.target.value }))}
              />
              <Input
                type="time"
                label="Hora de Término"
                value={newEvent.endTime}
                onChange={(e) => setNewEvent(prev => ({ ...prev, endTime: e.target.value }))}
              />
            </div>

            <Input
              label="Local (para eventos presenciais)"
              placeholder="Endereço ou local do evento"
              value={newEvent.location}
              onChange={(e) => setNewEvent(prev => ({ ...prev, location: e.target.value }))}
            />

            <Input
              label="Link Online (para eventos virtuais)"
              placeholder="Discord, Zoom, Twitch, etc."
              value={newEvent.onlineLink}
              onChange={(e) => setNewEvent(prev => ({ ...prev, onlineLink: e.target.value }))}
            />

            <Input
              type="number"
              label="Máximo de Participantes (opcional)"
              placeholder="Deixe vazio para ilimitado"
              value={newEvent.maxParticipants}
              onChange={(e) => setNewEvent(prev => ({ ...prev, maxParticipants: e.target.value }))}
            />

            <Input
              label="Tags"
              placeholder="Separe as tags por vírgula: torneio, valorant, premiação"
              value={newEvent.tags}
              onChange={(e) => setNewEvent(prev => ({ ...prev, tags: e.target.value }))}
            />
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onEventModalClose}>
              Cancelar
            </Button>
            <Button
              color="primary"
              isDisabled={!newEvent.title.trim() || !newEvent.description.trim() || !newEvent.startDate}
            >
              Criar Evento
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}

import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Divider,
  Input,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Breadcrumbs,
  BreadcrumbItem,
} from '@heroui/react';
import {
  ChatBubbleLeftRightIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  ClockIcon,
  MapPinIcon,
  LockClosedIcon,
  FireIcon,
} from '@heroicons/react/24/outline';

import { useAuth } from '@/contexts/AuthContext';
import { Forum, ForumTopic, ForumPost } from '@/types/community';

interface ForumSystemProps {
  gameId?: string;
  gameName?: string;
}

// Mock data for demonstration
const mockForums: Forum[] = [
  {
    id: '1',
    name: '<PERSON>uss<PERSON> Geral',
    description: 'Discussões gerais sobre jogos e gaming',
    category: 'Geral',
    moderatorIds: ['user1'],
    topicsCount: 156,
    postsCount: 1247,
    lastTopicId: 'topic1',
    lastPostAt: { seconds: Date.now() / 1000 - 1800 } as any,
    isActive: true,
    order: 1,
    createdAt: { seconds: Date.now() / 1000 - 86400 } as any,
  },
  {
    id: '2',
    name: 'Valorant',
    description: 'Estratégias, dicas e discussões sobre Valorant',
    category: 'Jogos',
    gameId: 'valorant',
    gameName: 'Valorant',
    moderatorIds: ['user2'],
    topicsCount: 89,
    postsCount: 567,
    lastTopicId: 'topic2',
    lastPostAt: { seconds: Date.now() / 1000 - 3600 } as any,
    isActive: true,
    order: 2,
    createdAt: { seconds: Date.now() / 1000 - 172800 } as any,
  },
  {
    id: '3',
    name: 'Elden Ring',
    description: 'Builds, lore e ajuda para Elden Ring',
    category: 'Jogos',
    gameId: 'elden-ring',
    gameName: 'Elden Ring',
    moderatorIds: ['user1', 'user3'],
    topicsCount: 234,
    postsCount: 1890,
    lastTopicId: 'topic3',
    lastPostAt: { seconds: Date.now() / 1000 - 7200 } as any,
    isActive: true,
    order: 3,
    createdAt: { seconds: Date.now() / 1000 - 259200 } as any,
  },
];

const mockTopics: ForumTopic[] = [
  {
    id: 'topic1',
    forumId: '1',
    title: 'Qual foi o jogo que mais te marcou em 2024?',
    content: 'Pessoal, estou curioso para saber quais jogos mais marcaram vocês este ano. Para mim foi definitivamente Elden Ring, que experiência incrível!',
    authorId: 'user1',
    authorName: 'GamerPro2024',
    authorAvatar: 'https://i.pravatar.cc/150?u=user1',
    tags: ['discussao', '2024', 'favoritos'],
    isPinned: true,
    isLocked: false,
    viewsCount: 245,
    repliesCount: 18,
    lastReplyId: 'post1',
    lastReplyAt: { seconds: Date.now() / 1000 - 1800 } as any,
    lastReplyAuthor: 'StreamerQueen',
    createdAt: { seconds: Date.now() / 1000 - 86400 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 1800 } as any,
  },
  {
    id: 'topic2',
    forumId: '2',
    title: 'Melhores agentes para iniciantes no Valorant',
    content: 'Galera, sou novo no Valorant e queria dicas de quais agentes são melhores para quem está começando. Ouvi falar que Sage é boa, mas queria mais opções.',
    authorId: 'user4',
    authorName: 'NoobMaster',
    authorAvatar: 'https://i.pravatar.cc/150?u=user4',
    tags: ['iniciantes', 'agentes', 'dicas'],
    isPinned: false,
    isLocked: false,
    viewsCount: 89,
    repliesCount: 12,
    lastReplyId: 'post2',
    lastReplyAt: { seconds: Date.now() / 1000 - 3600 } as any,
    lastReplyAuthor: 'ValorantPro',
    createdAt: { seconds: Date.now() / 1000 - 172800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 3600 } as any,
  },
];

export default function ForumSystem({ gameId, gameName }: ForumSystemProps) {
  const { currentUser } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [forums, setForums] = useState<Forum[]>(mockForums);
  const [topics, setTopics] = useState<ForumTopic[]>(mockTopics);
  const [selectedForum, setSelectedForum] = useState<string | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [newTopicTitle, setNewTopicTitle] = useState('');
  const [newTopicContent, setNewTopicContent] = useState('');
  const [newTopicTags, setNewTopicTags] = useState('');

  useEffect(() => {
    if (gameId) {
      const gameForum = forums.find(f => f.gameId === gameId);
      if (gameForum) {
        setSelectedForum(gameForum.id);
      }
    }
  }, [gameId, forums]);

  const handleCreateTopic = async () => {
    if (!currentUser || !newTopicTitle.trim() || !newTopicContent.trim()) return;

    const newTopic: ForumTopic = {
      id: `topic-${Date.now()}`,
      forumId: selectedForum!,
      title: newTopicTitle,
      content: newTopicContent,
      authorId: currentUser.uid,
      authorName: currentUser.displayName || 'Usuário',
      authorAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      tags: newTopicTags.split(',').map(tag => tag.trim()).filter(tag => tag),
      isPinned: false,
      isLocked: false,
      viewsCount: 0,
      repliesCount: 0,
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
    };

    setTopics(prev => [newTopic, ...prev]);
    setNewTopicTitle('');
    setNewTopicContent('');
    setNewTopicTags('');
    onClose();
  };

  const formatTimeAgo = (timestamp: any) => {
    const now = Date.now();
    const time = timestamp.seconds * 1000;
    const diff = now - time;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days}d atrás`;
    if (hours > 0) return `${hours}h atrás`;
    if (minutes > 0) return `${minutes}m atrás`;
    return 'Agora';
  };

  const filteredForums = forums.filter(forum => 
    !gameId || forum.gameId === gameId
  );

  const filteredTopics = topics.filter(topic => 
    (!selectedForum || topic.forumId === selectedForum) &&
    (!searchTerm || topic.title.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const currentForum = forums.find(f => f.id === selectedForum);

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumbs>
        <BreadcrumbItem onClick={() => setSelectedForum(null)}>
          Fóruns
        </BreadcrumbItem>
        {currentForum && (
          <BreadcrumbItem>{currentForum.name}</BreadcrumbItem>
        )}
      </Breadcrumbs>

      {/* Search and Actions */}
      <div className="flex items-center gap-4">
        <Input
          placeholder="Buscar tópicos..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
          className="flex-1"
        />
        {selectedForum && currentUser && (
          <Button
            color="primary"
            startContent={<PlusIcon className="w-4 h-4" />}
            onClick={onOpen}
          >
            Novo Tópico
          </Button>
        )}
      </div>

      {!selectedForum ? (
        /* Forums List */
        <div className="space-y-4">
          <h2 className="text-2xl font-bold">Fóruns da Comunidade</h2>
          
          <div className="space-y-3">
            {filteredForums.map((forum) => (
              <Card
                key={forum.id}
                isPressable
                onPress={() => setSelectedForum(forum.id)}
                className="hover:scale-[1.02] transition-transform"
              >
                <CardBody className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <ChatBubbleLeftRightIcon className="w-6 h-6 text-primary" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-lg">{forum.name}</h3>
                          {forum.gameId && (
                            <Chip size="sm" color="primary" variant="flat">
                              {forum.gameName}
                            </Chip>
                          )}
                        </div>
                        <p className="text-default-600 mb-3">{forum.description}</p>
                        
                        <div className="flex items-center gap-4 text-sm text-default-500">
                          <span>{forum.topicsCount} tópicos</span>
                          <span>{forum.postsCount} posts</span>
                          {forum.lastPostAt && (
                            <span>Último post {formatTimeAgo(forum.lastPostAt)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-sm text-default-500">
                        <FireIcon className="w-4 h-4" />
                        <span>Ativo</span>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      ) : (
        /* Topics List */
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">{currentForum?.name}</h2>
              <p className="text-default-600">{currentForum?.description}</p>
            </div>
            <div className="text-right text-sm text-default-500">
              <p>{currentForum?.topicsCount} tópicos</p>
              <p>{currentForum?.postsCount} posts</p>
            </div>
          </div>
          
          <div className="space-y-3">
            {filteredTopics.map((topic) => (
              <Card key={topic.id} isPressable className="hover:bg-default-50">
                <CardBody className="p-4">
                  <div className="flex items-start gap-4">
                    <Avatar
                      src={topic.authorAvatar}
                      name={topic.authorName}
                      size="md"
                    />
                    
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {topic.isPinned && (
                            <MapPinIcon className="w-4 h-4 text-warning" />
                          )}
                          {topic.isLocked && (
                            <LockClosedIcon className="w-4 h-4 text-danger" />
                          )}
                          <h4 className="font-semibold hover:text-primary cursor-pointer">
                            {topic.title}
                          </h4>
                        </div>
                        <div className="text-right text-sm text-default-500">
                          <div className="flex items-center gap-1">
                            <EyeIcon className="w-4 h-4" />
                            <span>{topic.viewsCount}</span>
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-default-600 text-sm mb-3 line-clamp-2">
                        {topic.content}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-default-500">
                            Por {topic.authorName}
                          </span>
                          <span className="text-sm text-default-500">
                            {formatTimeAgo(topic.createdAt)}
                          </span>
                          {topic.tags.length > 0 && (
                            <div className="flex gap-1">
                              {topic.tags.slice(0, 3).map((tag) => (
                                <Chip
                                  key={tag}
                                  size="sm"
                                  variant="flat"
                                  color="secondary"
                                  className="text-xs"
                                >
                                  #{tag}
                                </Chip>
                              ))}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <ChatBubbleLeftRightIcon className="w-4 h-4" />
                            <span>{topic.repliesCount}</span>
                          </div>
                          {topic.lastReplyAt && (
                            <div className="text-default-500">
                              <div className="flex items-center gap-1">
                                <ClockIcon className="w-4 h-4" />
                                <span>{formatTimeAgo(topic.lastReplyAt)}</span>
                              </div>
                              <p className="text-xs">por {topic.lastReplyAuthor}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Create Topic Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <h3>Criar Novo Tópico</h3>
          </ModalHeader>
          <ModalBody className="space-y-4">
            <Input
              label="Título do Tópico"
              placeholder="Digite um título claro e descritivo"
              value={newTopicTitle}
              onChange={(e) => setNewTopicTitle(e.target.value)}
              maxLength={100}
            />
            
            <Textarea
              label="Conteúdo"
              placeholder="Descreva seu tópico em detalhes..."
              value={newTopicContent}
              onChange={(e) => setNewTopicContent(e.target.value)}
              minRows={6}
              maxRows={12}
              maxLength={2000}
            />
            
            <Input
              label="Tags (opcional)"
              placeholder="Separe as tags por vírgula: dica, iniciante, estratégia"
              value={newTopicTags}
              onChange={(e) => setNewTopicTags(e.target.value)}
            />
            
            <div className="text-sm text-default-500">
              <p>Dicas para um bom tópico:</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Use um título claro e específico</li>
                <li>Forneça contexto suficiente no conteúdo</li>
                <li>Use tags relevantes para facilitar a busca</li>
                <li>Seja respeitoso e construtivo</li>
              </ul>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onClose}>
              Cancelar
            </Button>
            <Button
              color="primary"
              onPress={handleCreateTopic}
              isDisabled={!newTopicTitle.trim() || !newTopicContent.trim()}
            >
              Criar Tópico
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}

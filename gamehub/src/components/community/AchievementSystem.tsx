import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Progress,
  Avatar,
  Chip,
  Badge,
  Tabs,
  Tab,
  Button,
  Tooltip,
} from '@heroui/react';
import {
  TrophyIcon,
  StarIcon,
  FireIcon,
  SparklesIcon,
  ChartBarIcon,
  UserGroupIcon,
  ChatBubbleLeftIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';

import { useAuth } from '@/contexts/AuthContext';
import { Achievement, UserAchievement, Badge as BadgeType, UserBadge } from '@/types/community';

interface AchievementSystemProps {
  userId?: string;
}

// Mock data for demonstration
const mockAchievements: Achievement[] = [
  {
    id: '1',
    name: 'Primeiro Post',
    description: 'Compartilhe sua primeira experiência na comunidade',
    icon: '🎯',
    category: 'social',
    type: 'bronze',
    points: 10,
    requirements: [
      { type: 'posts_created', target: 1, current: 1 }
    ],
    isSecret: false,
    unlockedBy: ['user1', 'user2'],
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Siga 10 pessoas na comunidade',
    icon: '👥',
    category: 'social',
    type: 'silver',
    points: 25,
    requirements: [
      { type: 'following_count', target: 10, current: 7 }
    ],
    isSecret: false,
    unlockedBy: ['user1'],
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
  {
    id: '3',
    name: 'Conversador',
    description: 'Faça 50 comentários em posts',
    icon: '💬',
    category: 'content',
    type: 'silver',
    points: 30,
    requirements: [
      { type: 'comments_made', target: 50, current: 23 }
    ],
    isSecret: false,
    unlockedBy: [],
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
  {
    id: '4',
    name: 'Influenciador',
    description: 'Receba 100 curtidas em seus posts',
    icon: '⭐',
    category: 'content',
    type: 'gold',
    points: 50,
    requirements: [
      { type: 'likes_received', target: 100, current: 45 }
    ],
    isSecret: false,
    unlockedBy: [],
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
  {
    id: '5',
    name: 'Lenda da Comunidade',
    description: 'Conquista secreta para membros especiais',
    icon: '👑',
    category: 'special',
    type: 'legendary',
    points: 200,
    requirements: [
      { type: 'reputation', target: 1000, current: 450 }
    ],
    isSecret: true,
    unlockedBy: [],
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
];

const mockBadges: BadgeType[] = [
  {
    id: '1',
    name: 'Veterano',
    description: 'Membro há mais de 1 ano',
    icon: '🏆',
    color: 'gold',
    category: 'Tempo',
    rarity: 'rare',
    requirements: 'Seja membro por 365 dias',
    isActive: true,
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
  {
    id: '2',
    name: 'Moderador',
    description: 'Moderador da comunidade',
    icon: '🛡️',
    color: 'blue',
    category: 'Função',
    rarity: 'epic',
    requirements: 'Seja nomeado moderador',
    isActive: true,
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
  {
    id: '3',
    name: 'Criador de Conteúdo',
    description: 'Cria conteúdo de qualidade regularmente',
    icon: '✨',
    color: 'purple',
    category: 'Conteúdo',
    rarity: 'rare',
    requirements: 'Publique 20 posts de qualidade',
    isActive: true,
    createdAt: { seconds: Date.now() / 1000 } as any,
  },
];

const mockLeaderboard = [
  {
    userId: 'user1',
    userName: 'GamerPro2024',
    userAvatar: 'https://i.pravatar.cc/150?u=user1',
    totalPoints: 2450,
    level: 15,
    rank: 1,
    achievementsCount: 12,
    badgesCount: 5,
  },
  {
    userId: 'user2',
    userName: 'StreamerQueen',
    userAvatar: 'https://i.pravatar.cc/150?u=user2',
    totalPoints: 2180,
    level: 14,
    rank: 2,
    achievementsCount: 10,
    badgesCount: 3,
  },
  {
    userId: 'user3',
    userName: 'NoobMaster',
    userAvatar: 'https://i.pravatar.cc/150?u=user3',
    totalPoints: 1890,
    level: 12,
    rank: 3,
    achievementsCount: 8,
    badgesCount: 2,
  },
  {
    userId: 'user4',
    userName: 'ValorantPro',
    userAvatar: 'https://i.pravatar.cc/150?u=user4',
    totalPoints: 1650,
    level: 11,
    rank: 4,
    achievementsCount: 7,
    badgesCount: 4,
  },
  {
    userId: 'user5',
    userName: 'CasualGamer',
    userAvatar: 'https://i.pravatar.cc/150?u=user5',
    totalPoints: 1420,
    level: 10,
    rank: 5,
    achievementsCount: 6,
    badgesCount: 1,
  },
];

export default function AchievementSystem({ userId }: AchievementSystemProps) {
  const { currentUser } = useAuth();
  const [achievements, setAchievements] = useState<Achievement[]>(mockAchievements);
  const [badges, setBadges] = useState<BadgeType[]>(mockBadges);
  const [leaderboard, setLeaderboard] = useState(mockLeaderboard);
  const [userStats, setUserStats] = useState({
    totalPoints: 85,
    level: 3,
    rank: 156,
    achievementsUnlocked: 2,
    badgesEarned: 1,
    nextLevelPoints: 115,
  });

  const getAchievementTypeColor = (type: string) => {
    switch (type) {
      case 'bronze': return 'warning';
      case 'silver': return 'default';
      case 'gold': return 'secondary';
      case 'platinum': return 'primary';
      case 'legendary': return 'success';
      default: return 'default';
    }
  };

  const getBadgeRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'default';
      case 'rare': return 'primary';
      case 'epic': return 'secondary';
      case 'legendary': return 'warning';
      default: return 'default';
    }
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const isAchievementUnlocked = (achievement: Achievement) => {
    return achievement.unlockedBy.includes(currentUser?.uid || '');
  };

  const canViewAchievement = (achievement: Achievement) => {
    return !achievement.isSecret || isAchievementUnlocked(achievement);
  };

  return (
    <div className="space-y-6">
      {/* User Stats Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <TrophyIcon className="w-5 h-5 text-warning" />
            <h3 className="text-lg font-semibold">Seu Progresso</h3>
          </div>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-warning">{userStats.level}</div>
              <div className="text-sm text-default-500">Nível</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{userStats.totalPoints}</div>
              <div className="text-sm text-default-500">Pontos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success">#{userStats.rank}</div>
              <div className="text-sm text-default-500">Ranking</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary">{userStats.achievementsUnlocked}</div>
              <div className="text-sm text-default-500">Conquistas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-danger">{userStats.badgesEarned}</div>
              <div className="text-sm text-default-500">Badges</div>
            </div>
          </div>
          
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Progresso para o Nível {userStats.level + 1}</span>
              <span className="text-sm text-default-500">
                {userStats.totalPoints}/{userStats.nextLevelPoints} pontos
              </span>
            </div>
            <Progress
              value={calculateProgress(userStats.totalPoints, userStats.nextLevelPoints)}
              color="primary"
              className="w-full"
            />
          </div>
        </CardBody>
      </Card>

      {/* Tabs */}
      <Tabs
        variant="underlined"
        classNames={{
          tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
          cursor: "w-full bg-primary",
          tab: "max-w-fit px-0 h-12",
        }}
      >
        <Tab
          key="achievements"
          title={
            <div className="flex items-center gap-2">
              <TrophyIcon className="w-4 h-4" />
              <span>Conquistas</span>
            </div>
          }
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.filter(canViewAchievement).map((achievement) => {
                const isUnlocked = isAchievementUnlocked(achievement);
                const progress = achievement.requirements[0];
                const progressPercentage = calculateProgress(progress.current || 0, progress.target);

                return (
                  <Card
                    key={achievement.id}
                    className={`${isUnlocked ? 'bg-success/5 border-success/20' : 'opacity-75'}`}
                  >
                    <CardBody className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="text-3xl">{achievement.icon}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold">{achievement.name}</h4>
                            <Chip
                              size="sm"
                              color={getAchievementTypeColor(achievement.type) as any}
                              variant="flat"
                            >
                              {achievement.type}
                            </Chip>
                          </div>
                          <p className="text-sm text-default-600 mb-2">
                            {achievement.description}
                          </p>
                          
                          {!isUnlocked && (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-xs">
                                <span>Progresso</span>
                                <span>{progress.current || 0}/{progress.target}</span>
                              </div>
                              <Progress
                                value={progressPercentage}
                                color="primary"
                                size="sm"
                              />
                            </div>
                          )}
                          
                          <div className="flex items-center justify-between mt-3">
                            <div className="flex items-center gap-1 text-xs text-default-500">
                              <StarIcon className="w-3 h-3" />
                              <span>{achievement.points} pontos</span>
                            </div>
                            {isUnlocked && (
                              <Chip size="sm" color="success" variant="solid">
                                ✓ Desbloqueada
                              </Chip>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                );
              })}
            </div>
          </div>
        </Tab>

        <Tab
          key="badges"
          title={
            <div className="flex items-center gap-2">
              <SparklesIcon className="w-4 h-4" />
              <span>Badges</span>
            </div>
          }
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {badges.map((badge) => (
                <Card key={badge.id}>
                  <CardBody className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="text-3xl">{badge.icon}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold">{badge.name}</h4>
                          <Chip
                            size="sm"
                            color={getBadgeRarityColor(badge.rarity) as any}
                            variant="flat"
                          >
                            {badge.rarity}
                          </Chip>
                        </div>
                        <p className="text-sm text-default-600 mb-2">
                          {badge.description}
                        </p>
                        <p className="text-xs text-default-500">
                          {badge.requirements}
                        </p>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        </Tab>

        <Tab
          key="leaderboard"
          title={
            <div className="flex items-center gap-2">
              <ChartBarIcon className="w-4 h-4" />
              <span>Ranking</span>
            </div>
          }
        >
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Top Jogadores da Comunidade</h3>
              </CardHeader>
              <CardBody className="pt-0">
                <div className="space-y-3">
                  {leaderboard.map((user) => (
                    <div
                      key={user.userId}
                      className={`flex items-center gap-4 p-4 rounded-lg ${
                        user.rank <= 3 ? 'bg-gradient-to-r from-warning/10 to-primary/10' : 'bg-default-50'
                      }`}
                    >
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                        user.rank === 1 ? 'bg-yellow-500 text-white' :
                        user.rank === 2 ? 'bg-gray-400 text-white' :
                        user.rank === 3 ? 'bg-orange-600 text-white' :
                        'bg-default-200'
                      }`}>
                        {user.rank}
                      </div>
                      
                      <Avatar
                        src={user.userAvatar}
                        name={user.userName}
                        size="md"
                      />
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold">{user.userName}</h4>
                          <Chip size="sm" color="primary" variant="flat">
                            Nível {user.level}
                          </Chip>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-default-500">
                          <span>{user.achievementsCount} conquistas</span>
                          <span>{user.badgesCount} badges</span>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-lg font-bold text-primary">
                          {user.totalPoints.toLocaleString()}
                        </div>
                        <div className="text-xs text-default-500">pontos</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
        </Tab>
      </Tabs>
    </div>
  );
}

import { useState, useEffect, useRef } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Input,
  Button,
  Avatar,
  Chip,
  Divider,
  ScrollShadow,
  Badge,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from '@heroui/react';
import {
  PaperAirplaneIcon,
  FaceSmileIcon,
  PhotoIcon,
  EllipsisVerticalIcon,
  UserPlusIcon,
  PhoneIcon,
  VideoCameraIcon,
} from '@heroicons/react/24/outline';

import { useAuth } from '@/contexts/AuthContext';
import { Message, ChatRoom, UserPresence } from '@/types/community';

interface ChatSystemProps {
  chatRoomId?: string;
  isOpen: boolean;
  onClose: () => void;
}

// Mock data for demonstration
const mockMessages: Message[] = [
  {
    id: '1',
    chatRoomId: 'room1',
    senderId: 'user1',
    senderName: 'GamerPro2024',
    senderAvatar: 'https://i.pravatar.cc/150?u=user1',
    content: 'Alguém quer jogar Valorant agora?',
    type: 'text',
    isEdited: false,
    readBy: ['user1', 'user2'],
    createdAt: { seconds: Date.now() / 1000 - 3600 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 3600 } as any,
  },
  {
    id: '2',
    chatRoomId: 'room1',
    senderId: 'user2',
    senderName: 'StreamerQueen',
    senderAvatar: 'https://i.pravatar.cc/150?u=user2',
    content: 'Eu topo! Qual o rank?',
    type: 'text',
    isEdited: false,
    readBy: ['user1', 'user2'],
    createdAt: { seconds: Date.now() / 1000 - 3000 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 3000 } as any,
  },
  {
    id: '3',
    chatRoomId: 'room1',
    senderId: 'user1',
    senderName: 'GamerPro2024',
    senderAvatar: 'https://i.pravatar.cc/150?u=user1',
    content: 'Sou Diamante 2, você?',
    type: 'text',
    isEdited: false,
    readBy: ['user1'],
    createdAt: { seconds: Date.now() / 1000 - 1800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 1800 } as any,
  },
];

const mockChatRooms: ChatRoom[] = [
  {
    id: 'room1',
    name: 'Valorant Squad',
    type: 'group',
    participantIds: ['user1', 'user2', 'user3'],
    creatorId: 'user1',
    lastMessageId: '3',
    lastMessageAt: { seconds: Date.now() / 1000 - 1800 } as any,
    isActive: true,
    createdAt: { seconds: Date.now() / 1000 - 86400 } as any,
  },
  {
    id: 'room2',
    type: 'direct',
    participantIds: ['user1', 'user4'],
    creatorId: 'user1',
    lastMessageId: '4',
    lastMessageAt: { seconds: Date.now() / 1000 - 7200 } as any,
    isActive: true,
    createdAt: { seconds: Date.now() / 1000 - 172800 } as any,
  },
];

const mockOnlineUsers: UserPresence[] = [
  {
    userId: 'user1',
    status: 'online',
    lastSeen: { seconds: Date.now() / 1000 } as any,
    currentActivity: 'Navegando na comunidade',
  },
  {
    userId: 'user2',
    status: 'online',
    lastSeen: { seconds: Date.now() / 1000 } as any,
    currentActivity: 'Jogando Valorant',
    currentGame: 'Valorant',
  },
  {
    userId: 'user3',
    status: 'away',
    lastSeen: { seconds: Date.now() / 1000 - 1800 } as any,
  },
];

export default function ChatSystem({ chatRoomId, isOpen, onClose }: ChatSystemProps) {
  const { currentUser } = useAuth();
  const [messages, setMessages] = useState<Message[]>(mockMessages);
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>(mockChatRooms);
  const [onlineUsers, setOnlineUsers] = useState<UserPresence[]>(mockOnlineUsers);
  const [selectedRoom, setSelectedRoom] = useState<string>(chatRoomId || 'room1');
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (chatRoomId) {
      setSelectedRoom(chatRoomId);
    }
  }, [chatRoomId]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!currentUser || !newMessage.trim()) return;

    const message: Message = {
      id: `msg-${Date.now()}`,
      chatRoomId: selectedRoom,
      senderId: currentUser.uid,
      senderName: currentUser.displayName || 'Usuário',
      senderAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      content: newMessage,
      type: 'text',
      isEdited: false,
      readBy: [currentUser.uid],
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessageTime = (timestamp: any) => {
    return new Date(timestamp.seconds * 1000).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'success';
      case 'away': return 'warning';
      case 'busy': return 'danger';
      default: return 'default';
    }
  };

  const currentRoom = chatRooms.find(room => room.id === selectedRoom);
  const roomMessages = messages.filter(msg => msg.chatRoomId === selectedRoom);

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 w-96 h-[500px] z-50">
      <Card className="h-full flex flex-col">
        {/* Chat Header */}
        <CardHeader className="flex flex-row items-center justify-between p-4 border-b">
          <div className="flex items-center gap-3">
            <Avatar
              src="https://i.pravatar.cc/150?u=room"
              name={currentRoom?.name || 'Chat'}
              size="sm"
            />
            <div>
              <p className="font-semibold text-sm">
                {currentRoom?.name || 'Chat Direto'}
              </p>
              <p className="text-xs text-default-500">
                {currentRoom?.participantIds.length} participantes
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <Button isIconOnly size="sm" variant="light">
              <PhoneIcon className="w-4 h-4" />
            </Button>
            <Button isIconOnly size="sm" variant="light">
              <VideoCameraIcon className="w-4 h-4" />
            </Button>
            <Dropdown>
              <DropdownTrigger>
                <Button isIconOnly size="sm" variant="light">
                  <EllipsisVerticalIcon className="w-4 h-4" />
                </Button>
              </DropdownTrigger>
              <DropdownMenu>
                <DropdownItem startContent={<UserPlusIcon className="w-4 h-4" />}>
                  Adicionar Pessoa
                </DropdownItem>
                <DropdownItem>Ver Perfil</DropdownItem>
                <DropdownItem className="text-danger" color="danger">
                  Sair do Chat
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
            <Button isIconOnly size="sm" variant="light" onClick={onClose}>
              ✕
            </Button>
          </div>
        </CardHeader>

        {/* Messages Area */}
        <CardBody className="flex-1 p-0 overflow-hidden">
          <ScrollShadow className="h-full p-4">
            <div className="space-y-4">
              {roomMessages.map((message, index) => {
                const isOwn = message.senderId === currentUser?.uid;
                const showAvatar = index === 0 || 
                  roomMessages[index - 1].senderId !== message.senderId;

                return (
                  <div
                    key={message.id}
                    className={`flex gap-2 ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}
                  >
                    {showAvatar && !isOwn ? (
                      <Avatar
                        src={message.senderAvatar}
                        name={message.senderName}
                        size="sm"
                      />
                    ) : (
                      <div className="w-8" />
                    )}
                    
                    <div className={`flex flex-col ${isOwn ? 'items-end' : 'items-start'} max-w-[70%]`}>
                      {showAvatar && !isOwn && (
                        <p className="text-xs text-default-500 mb-1">
                          {message.senderName}
                        </p>
                      )}
                      
                      <div
                        className={`px-3 py-2 rounded-lg ${
                          isOwn
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-default-100'
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                      </div>
                      
                      <p className="text-xs text-default-400 mt-1">
                        {formatMessageTime(message.createdAt)}
                        {isOwn && (
                          <span className="ml-1">
                            {message.readBy.length > 1 ? '✓✓' : '✓'}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                );
              })}
              
              {isTyping && (
                <div className="flex items-center gap-2">
                  <Avatar size="sm" />
                  <div className="bg-default-100 rounded-lg px-3 py-2">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-default-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-default-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-default-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </ScrollShadow>
        </CardBody>

        {/* Message Input */}
        <div className="p-4 border-t">
          <div className="flex items-end gap-2">
            <Button isIconOnly size="sm" variant="light">
              <PhotoIcon className="w-4 h-4" />
            </Button>
            <Button isIconOnly size="sm" variant="light">
              <FaceSmileIcon className="w-4 h-4" />
            </Button>
            
            <Input
              placeholder="Digite sua mensagem..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
              size="sm"
            />
            
            <Button
              isIconOnly
              color="primary"
              size="sm"
              onClick={handleSendMessage}
              isDisabled={!newMessage.trim()}
            >
              <PaperAirplaneIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>

      {/* Online Users Sidebar */}
      <Card className="absolute -left-64 top-0 w-60 h-full">
        <CardHeader className="p-4 border-b">
          <h3 className="font-semibold">Online Agora</h3>
        </CardHeader>
        <CardBody className="p-4">
          <div className="space-y-3">
            {onlineUsers.map((user) => (
              <div key={user.userId} className="flex items-center gap-3">
                <div className="relative">
                  <Avatar
                    src={`https://i.pravatar.cc/150?u=${user.userId}`}
                    name="User"
                    size="sm"
                  />
                  <Badge
                    content=""
                    color={getStatusColor(user.status) as any}
                    shape="circle"
                    placement="bottom-right"
                    className="w-3 h-3"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm truncate">
                    User {user.userId}
                  </p>
                  {user.currentActivity && (
                    <p className="text-xs text-default-500 truncate">
                      {user.currentActivity}
                    </p>
                  )}
                  {user.currentGame && (
                    <Chip size="sm" color="primary" variant="flat" className="text-xs">
                      {user.currentGame}
                    </Chip>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
}

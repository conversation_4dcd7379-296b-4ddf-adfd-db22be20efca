import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  Timestamp,
  increment,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
} from 'firebase/firestore';
import { db } from './firebase';
import type {
  User,
  Post,
  Comment,
  Group,
  Tournament,
  Notification,
  Achievement,
  Game,
  CollectionName,
  QueryOptions,
} from '../types/database';

// Generic CRUD operations
export class FirestoreService {
  // Create document
  static async create<T>(collectionName: CollectionName, data: Omit<T, 'id'>): Promise<string> {
    const docRef = await addDoc(collection(db, collectionName), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    return docRef.id;
  }

  // Create document with custom ID
  static async createWithId<T>(
    collectionName: CollectionName,
    id: string,
    data: Omit<T, 'id'>
  ): Promise<void> {
    await setDoc(doc(db, collectionName, id), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
  }

  // Get document by ID
  static async getById<T>(collectionName: CollectionName, id: string): Promise<T | null> {
    const docRef = doc(db, collectionName, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as T;
    }
    return null;
  }

  // Update document
  static async update(
    collectionName: CollectionName,
    id: string,
    data: Partial<any>
  ): Promise<void> {
    const docRef = doc(db, collectionName, id);
    await updateDoc(docRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });
  }

  // Delete document
  static async delete(collectionName: CollectionName, id: string): Promise<void> {
    const docRef = doc(db, collectionName, id);
    await deleteDoc(docRef);
  }

  // Query documents
  static async query<T>(
    collectionName: CollectionName,
    options: QueryOptions = { limit: 10 }
  ): Promise<T[]> {
    let q = query(collection(db, collectionName));

    // Add where clauses
    if (options.where) {
      options.where.forEach(({ field, operator, value }) => {
        q = query(q, where(field, operator, value));
      });
    }

    // Add ordering
    if (options.orderBy) {
      q = query(q, orderBy(options.orderBy, options.orderDirection || 'desc'));
    }

    // Add pagination
    if (options.lastDoc) {
      q = query(q, startAfter(options.lastDoc));
    }

    q = query(q, limit(options.limit));

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as T[];
  }

  // Real-time listener
  static onSnapshot<T>(
    collectionName: CollectionName,
    callback: (data: T[]) => void,
    options: QueryOptions = { limit: 10 }
  ): () => void {
    let q = query(collection(db, collectionName));

    if (options.where) {
      options.where.forEach(({ field, operator, value }) => {
        q = query(q, where(field, operator, value));
      });
    }

    if (options.orderBy) {
      q = query(q, orderBy(options.orderBy, options.orderDirection || 'desc'));
    }

    q = query(q, limit(options.limit));

    return onSnapshot(q, (querySnapshot) => {
      const data = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as T[];
      callback(data);
    });
  }
}

// Specialized services for each entity
export class UserService {
  static async createUser(userData: Omit<User, 'id'>): Promise<void> {
    await FirestoreService.createWithId('users', userData.uid, userData);
  }

  static async getUser(uid: string): Promise<User | null> {
    return FirestoreService.getById<User>('users', uid);
  }

  static async updateUser(uid: string, data: Partial<User>): Promise<void> {
    await FirestoreService.update('users', uid, data);
  }

  static async followUser(currentUserId: string, targetUserId: string): Promise<void> {
    // Add to current user's following list
    await FirestoreService.update('users', currentUserId, {
      following: arrayUnion(targetUserId),
    });

    // Add to target user's followers list
    await FirestoreService.update('users', targetUserId, {
      followers: arrayUnion(currentUserId),
    });
  }

  static async unfollowUser(currentUserId: string, targetUserId: string): Promise<void> {
    // Remove from current user's following list
    await FirestoreService.update('users', currentUserId, {
      following: arrayRemove(targetUserId),
    });

    // Remove from target user's followers list
    await FirestoreService.update('users', targetUserId, {
      followers: arrayRemove(currentUserId),
    });
  }

  static async updateOnlineStatus(uid: string, isOnline: boolean): Promise<void> {
    await FirestoreService.update('users', uid, {
      isOnline,
      lastActive: serverTimestamp(),
    });
  }
}

export class PostService {
  static async createPost(postData: Omit<Post, 'id'>): Promise<string> {
    return FirestoreService.create<Post>('posts', postData);
  }

  static async getPost(id: string): Promise<Post | null> {
    return FirestoreService.getById<Post>('posts', id);
  }

  static async updatePost(id: string, data: Partial<Post>): Promise<void> {
    await FirestoreService.update('posts', id, data);
  }

  static async deletePost(id: string): Promise<void> {
    await FirestoreService.delete('posts', id);
  }

  static async likePost(postId: string, userId: string): Promise<void> {
    await FirestoreService.update('posts', postId, {
      likes: arrayUnion(userId),
      likesCount: increment(1),
    });
  }

  static async unlikePost(postId: string, userId: string): Promise<void> {
    await FirestoreService.update('posts', postId, {
      likes: arrayRemove(userId),
      likesCount: increment(-1),
    });
  }

  static async getFeedPosts(options: QueryOptions = { limit: 10 }): Promise<Post[]> {
    return FirestoreService.query<Post>('posts', {
      ...options,
      where: [{ field: 'isPublic', operator: '==', value: true }],
      orderBy: 'createdAt',
      orderDirection: 'desc',
    });
  }

  static async getUserPosts(userId: string, options: QueryOptions = { limit: 10 }): Promise<Post[]> {
    return FirestoreService.query<Post>('posts', {
      ...options,
      where: [{ field: 'authorId', operator: '==', value: userId }],
      orderBy: 'createdAt',
      orderDirection: 'desc',
    });
  }

  static async getGroupPosts(groupId: string, options: QueryOptions = { limit: 10 }): Promise<Post[]> {
    return FirestoreService.query<Post>('posts', {
      ...options,
      where: [{ field: 'groupId', operator: '==', value: groupId }],
      orderBy: 'createdAt',
      orderDirection: 'desc',
    });
  }
}

export class CommentService {
  static async createComment(commentData: Omit<Comment, 'id'>): Promise<string> {
    const commentId = await FirestoreService.create<Comment>('comments', commentData);
    
    // Increment post comment count
    await FirestoreService.update('posts', commentData.postId, {
      commentsCount: increment(1),
    });

    return commentId;
  }

  static async getPostComments(postId: string, options: QueryOptions = { limit: 20 }): Promise<Comment[]> {
    return FirestoreService.query<Comment>('comments', {
      ...options,
      where: [{ field: 'postId', operator: '==', value: postId }],
      orderBy: 'createdAt',
      orderDirection: 'asc',
    });
  }

  static async likeComment(commentId: string, userId: string): Promise<void> {
    await FirestoreService.update('comments', commentId, {
      likes: arrayUnion(userId),
      likesCount: increment(1),
    });
  }

  static async unlikeComment(commentId: string, userId: string): Promise<void> {
    await FirestoreService.update('comments', commentId, {
      likes: arrayRemove(userId),
      likesCount: increment(-1),
    });
  }
}

export class GroupService {
  static async createGroup(groupData: Omit<Group, 'id'>): Promise<string> {
    return FirestoreService.create<Group>('groups', groupData);
  }

  static async getGroup(id: string): Promise<Group | null> {
    return FirestoreService.getById<Group>('groups', id);
  }

  static async joinGroup(groupId: string, userId: string): Promise<void> {
    await FirestoreService.update('groups', groupId, {
      members: arrayUnion(userId),
      memberCount: increment(1),
    });
  }

  static async leaveGroup(groupId: string, userId: string): Promise<void> {
    await FirestoreService.update('groups', groupId, {
      members: arrayRemove(userId),
      memberCount: increment(-1),
    });
  }

  static async getPublicGroups(options: QueryOptions = { limit: 10 }): Promise<Group[]> {
    return FirestoreService.query<Group>('groups', {
      ...options,
      where: [{ field: 'privacy', operator: '==', value: 'public' }],
      orderBy: 'memberCount',
      orderDirection: 'desc',
    });
  }

  static async getUserGroups(userId: string): Promise<Group[]> {
    return FirestoreService.query<Group>('groups', {
      where: [{ field: 'members', operator: 'array-contains', value: userId }],
      orderBy: 'lastActivity',
      orderDirection: 'desc',
      limit: 50,
    });
  }
}

// Game Service
export class GameService {
  private static readonly COLLECTION = 'games';

  // Get all games with pagination and filters
  static async getGames(options: {
    limit?: number;
    startAfter?: any;
    genre?: string;
    platform?: string;
    minRating?: number;
    searchTerm?: string;
    sortBy?: 'title' | 'rating' | 'playerCount' | 'releaseDate';
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<Game[]> {
    const {
      limit: limitCount = 20,
      startAfter,
      genre,
      platform,
      minRating,
      searchTerm,
      sortBy = 'playerCount',
      sortOrder = 'desc'
    } = options;

    let q = query(collection(db, this.COLLECTION));

    // Apply filters
    if (genre) {
      q = query(q, where('genres', 'array-contains', genre));
    }
    if (platform) {
      q = query(q, where('platforms', 'array-contains', platform));
    }
    if (minRating) {
      q = query(q, where('rating', '>=', minRating));
    }

    // Apply sorting
    q = query(q, orderBy(sortBy, sortOrder));

    // Apply pagination
    if (startAfter) {
      q = query(q, startAfter(startAfter));
    }
    q = query(q, limit(limitCount));

    const snapshot = await getDocs(q);
    let games = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Game));

    // Apply text search filter (client-side for now)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      games = games.filter(game =>
        game.title.toLowerCase().includes(searchLower) ||
        game.developer.toLowerCase().includes(searchLower) ||
        game.publisher.toLowerCase().includes(searchLower) ||
        game.genres.some(genre => genre.toLowerCase().includes(searchLower)) ||
        game.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return games;
  }

  // Get popular games
  static async getPopularGames(limit = 10): Promise<Game[]> {
    const q = query(
      collection(db, this.COLLECTION),
      where('isActive', '==', true),
      orderBy('playerCount', 'desc'),
      orderBy('rating', 'desc'),
      limit(limit)
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Game));
  }

  // Get games by genre
  static async getGamesByGenre(genre: string, limit = 20): Promise<Game[]> {
    const q = query(
      collection(db, this.COLLECTION),
      where('genres', 'array-contains', genre),
      where('isActive', '==', true),
      orderBy('rating', 'desc'),
      limit(limit)
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Game));
  }

  // Get games by platform
  static async getGamesByPlatform(platform: string, limit = 20): Promise<Game[]> {
    const q = query(
      collection(db, this.COLLECTION),
      where('platforms', 'array-contains', platform),
      where('isActive', '==', true),
      orderBy('rating', 'desc'),
      limit(limit)
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Game));
  }

  // Get game by ID
  static async getGameById(gameId: string): Promise<Game | null> {
    const docRef = doc(db, this.COLLECTION, gameId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as Game;
    }
    return null;
  }

  // Add game to user's favorites
  static async addToFavorites(userId: string, gameId: string): Promise<void> {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      favoriteGames: arrayUnion(gameId),
      updatedAt: serverTimestamp()
    });
  }

  // Remove game from user's favorites
  static async removeFromFavorites(userId: string, gameId: string): Promise<void> {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      favoriteGames: arrayRemove(gameId),
      updatedAt: serverTimestamp()
    });
  }

  // Get user's favorite games
  static async getUserFavoriteGames(userId: string): Promise<Game[]> {
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) return [];

    const userData = userDoc.data();
    const favoriteGameIds = userData.favoriteGames || [];

    if (favoriteGameIds.length === 0) return [];

    // Get games in batches (Firestore 'in' query limit is 10)
    const games: Game[] = [];
    for (let i = 0; i < favoriteGameIds.length; i += 10) {
      const batch = favoriteGameIds.slice(i, i + 10);
      const q = query(
        collection(db, this.COLLECTION),
        where('__name__', 'in', batch)
      );
      const snapshot = await getDocs(q);
      games.push(...snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Game)));
    }

    return games;
  }

  // Update game player count
  static async updatePlayerCount(gameId: string, playerCount: number): Promise<void> {
    const gameRef = doc(db, this.COLLECTION, gameId);
    await updateDoc(gameRef, {
      playerCount,
      updatedAt: serverTimestamp()
    });
  }

  // Get all unique genres
  static async getAllGenres(): Promise<string[]> {
    const snapshot = await getDocs(collection(db, this.COLLECTION));
    const genresSet = new Set<string>();

    snapshot.docs.forEach(doc => {
      const game = doc.data() as Game;
      game.genres.forEach(genre => genresSet.add(genre));
    });

    return Array.from(genresSet).sort();
  }

  // Get all unique platforms
  static async getAllPlatforms(): Promise<string[]> {
    const snapshot = await getDocs(collection(db, this.COLLECTION));
    const platformsSet = new Set<string>();

    snapshot.docs.forEach(doc => {
      const game = doc.data() as Game;
      game.platforms.forEach(platform => platformsSet.add(platform));
    });

    return Array.from(platformsSet).sort();
  }
}

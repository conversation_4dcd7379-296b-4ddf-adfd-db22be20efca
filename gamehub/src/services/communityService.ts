import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  arrayUnion,
  arrayRemove,
  increment,
  onSnapshot,
  Timestamp,
} from 'firebase/firestore';
import { db } from '@/config/firebase';
import {
  UserProfile,
  Post,
  Comment,
  Like,
  Group,
  GroupMembership,
  Event,
  ChatRoom,
  Message,
  Forum,
  ForumTopic,
  ForumPost,
  Follow,
  Notification,
  Activity,
  Achievement,
  UserAchievement,
  Badge,
  UserBadge,
  MentorshipRequest,
  MentorProfile,
  UserPresence,
} from '@/types/community';

// User Profile Service
export class UserProfileService {
  private static readonly COLLECTION = 'userProfiles';

  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    const docRef = doc(db, this.COLLECTION, userId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as UserProfile;
    }
    return null;
  }

  static async updateUserProfile(userId: string, data: Partial<UserProfile>): Promise<void> {
    const docRef = doc(db, this.COLLECTION, userId);
    await updateDoc(docRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });
  }

  static async searchUsers(query: string, limit: number = 20): Promise<UserProfile[]> {
    const q = query(
      collection(db, this.COLLECTION),
      where('username', '>=', query.toLowerCase()),
      where('username', '<=', query.toLowerCase() + '\uf8ff'),
      orderBy('username'),
      limit(limit)
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as UserProfile));
  }

  static async updateOnlineStatus(userId: string, isOnline: boolean): Promise<void> {
    const docRef = doc(db, this.COLLECTION, userId);
    await updateDoc(docRef, {
      isOnline,
      lastActive: serverTimestamp(),
    });
  }
}

// Social Connections Service
export class SocialService {
  private static readonly FOLLOWS_COLLECTION = 'follows';

  static async followUser(followerId: string, followingId: string): Promise<void> {
    const followDoc = {
      followerId,
      followingId,
      createdAt: serverTimestamp(),
    };

    await addDoc(collection(db, this.FOLLOWS_COLLECTION), followDoc);

    // Update counters
    await Promise.all([
      updateDoc(doc(db, 'userProfiles', followerId), {
        followingCount: increment(1),
      }),
      updateDoc(doc(db, 'userProfiles', followingId), {
        followersCount: increment(1),
      }),
    ]);
  }

  static async unfollowUser(followerId: string, followingId: string): Promise<void> {
    const q = query(
      collection(db, this.FOLLOWS_COLLECTION),
      where('followerId', '==', followerId),
      where('followingId', '==', followingId)
    );

    const snapshot = await getDocs(q);
    const promises = snapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(promises);

    // Update counters
    await Promise.all([
      updateDoc(doc(db, 'userProfiles', followerId), {
        followingCount: increment(-1),
      }),
      updateDoc(doc(db, 'userProfiles', followingId), {
        followersCount: increment(-1),
      }),
    ]);
  }

  static async getFollowers(userId: string, limitCount: number = 20): Promise<UserProfile[]> {
    const q = query(
      collection(db, this.FOLLOWS_COLLECTION),
      where('followingId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    const followerIds = snapshot.docs.map(doc => doc.data().followerId);

    if (followerIds.length === 0) return [];

    const userProfiles = await Promise.all(
      followerIds.map(id => UserProfileService.getUserProfile(id))
    );

    return userProfiles.filter(profile => profile !== null) as UserProfile[];
  }

  static async getFollowing(userId: string, limitCount: number = 20): Promise<UserProfile[]> {
    const q = query(
      collection(db, this.FOLLOWS_COLLECTION),
      where('followerId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    const followingIds = snapshot.docs.map(doc => doc.data().followingId);

    if (followingIds.length === 0) return [];

    const userProfiles = await Promise.all(
      followingIds.map(id => UserProfileService.getUserProfile(id))
    );

    return userProfiles.filter(profile => profile !== null) as UserProfile[];
  }

  static async isFollowing(followerId: string, followingId: string): Promise<boolean> {
    const q = query(
      collection(db, this.FOLLOWS_COLLECTION),
      where('followerId', '==', followerId),
      where('followingId', '==', followingId)
    );

    const snapshot = await getDocs(q);
    return !snapshot.empty;
  }
}

// Posts Service
export class PostsService {
  private static readonly COLLECTION = 'posts';
  private static readonly COMMENTS_COLLECTION = 'comments';
  private static readonly LIKES_COLLECTION = 'likes';

  static async createPost(post: Omit<Post, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const postDoc = {
      ...post,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const docRef = await addDoc(collection(db, this.COLLECTION), postDoc);

    // Update user's posts count
    await updateDoc(doc(db, 'userProfiles', post.authorId), {
      postsCount: increment(1),
    });

    return docRef.id;
  }

  static async getPosts(options: {
    userId?: string;
    gameId?: string;
    following?: string[];
    limit?: number;
    startAfter?: any;
  } = {}): Promise<Post[]> {
    const { userId, gameId, following, limit: limitCount = 20, startAfter } = options;

    let q = query(collection(db, this.COLLECTION));

    if (userId) {
      q = query(q, where('authorId', '==', userId));
    } else if (gameId) {
      q = query(q, where('gameId', '==', gameId));
    } else if (following && following.length > 0) {
      q = query(q, where('authorId', 'in', following));
    }

    q = query(q, orderBy('createdAt', 'desc'));

    if (startAfter) {
      q = query(q, startAfter(startAfter));
    }

    q = query(q, limit(limitCount));

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Post));
  }

  static async likePost(userId: string, postId: string): Promise<void> {
    const likeDoc = {
      userId,
      targetId: postId,
      targetType: 'post',
      createdAt: serverTimestamp(),
    };

    await addDoc(collection(db, this.LIKES_COLLECTION), likeDoc);

    // Update post likes count
    await updateDoc(doc(db, this.COLLECTION, postId), {
      likesCount: increment(1),
    });
  }

  static async unlikePost(userId: string, postId: string): Promise<void> {
    const q = query(
      collection(db, this.LIKES_COLLECTION),
      where('userId', '==', userId),
      where('targetId', '==', postId),
      where('targetType', '==', 'post')
    );

    const snapshot = await getDocs(q);
    const promises = snapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(promises);

    // Update post likes count
    await updateDoc(doc(db, this.COLLECTION, postId), {
      likesCount: increment(-1),
    });
  }

  static async addComment(comment: Omit<Comment, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const commentDoc = {
      ...comment,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const docRef = await addDoc(collection(db, this.COMMENTS_COLLECTION), commentDoc);

    // Update post comments count
    await updateDoc(doc(db, this.COLLECTION, comment.postId), {
      commentsCount: increment(1),
    });

    return docRef.id;
  }

  static async getComments(postId: string, limitCount: number = 20): Promise<Comment[]> {
    const q = query(
      collection(db, this.COMMENTS_COLLECTION),
      where('postId', '==', postId),
      orderBy('createdAt', 'asc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Comment));
  }
}

// Groups Service
export class GroupsService {
  private static readonly COLLECTION = 'groups';
  private static readonly MEMBERSHIPS_COLLECTION = 'groupMemberships';

  static async createGroup(group: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const groupDoc = {
      ...group,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastActivity: serverTimestamp(),
    };

    const docRef = await addDoc(collection(db, this.COLLECTION), groupDoc);

    // Add creator as admin
    await this.joinGroup(docRef.id, group.creatorId, 'admin');

    return docRef.id;
  }

  static async getGroups(options: {
    category?: string;
    gameId?: string;
    userId?: string;
    limit?: number;
  } = {}): Promise<Group[]> {
    const { category, gameId, userId, limit: limitCount = 20 } = options;

    let q = query(collection(db, this.COLLECTION));

    if (category) {
      q = query(q, where('category', '==', category));
    }
    if (gameId) {
      q = query(q, where('gameId', '==', gameId));
    }
    if (userId) {
      q = query(q, where('memberIds', 'array-contains', userId));
    }

    q = query(q, orderBy('lastActivity', 'desc'), limit(limitCount));

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Group));
  }

  static async joinGroup(groupId: string, userId: string, role: 'member' | 'moderator' | 'admin' = 'member'): Promise<void> {
    const membershipDoc = {
      groupId,
      userId,
      role,
      joinedAt: serverTimestamp(),
      status: 'active',
    };

    await addDoc(collection(db, this.MEMBERSHIPS_COLLECTION), membershipDoc);

    // Update group member list and count
    await updateDoc(doc(db, this.COLLECTION, groupId), {
      memberIds: arrayUnion(userId),
      membersCount: increment(1),
    });
  }

  static async leaveGroup(groupId: string, userId: string): Promise<void> {
    const q = query(
      collection(db, this.MEMBERSHIPS_COLLECTION),
      where('groupId', '==', groupId),
      where('userId', '==', userId)
    );

    const snapshot = await getDocs(q);
    const promises = snapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(promises);

    // Update group member list and count
    await updateDoc(doc(db, this.COLLECTION, groupId), {
      memberIds: arrayRemove(userId),
      membersCount: increment(-1),
    });
  }
}

// Events Service
export class EventsService {
  private static readonly COLLECTION = 'events';

  static async createEvent(event: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const eventDoc = {
      ...event,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    const docRef = await addDoc(collection(db, this.COLLECTION), eventDoc);
    return docRef.id;
  }

  static async getEvents(options: {
    type?: string;
    gameId?: string;
    groupId?: string;
    upcoming?: boolean;
    limit?: number;
  } = {}): Promise<Event[]> {
    const { type, gameId, groupId, upcoming, limit: limitCount = 20 } = options;

    let q = query(collection(db, this.COLLECTION));

    if (type) {
      q = query(q, where('type', '==', type));
    }
    if (gameId) {
      q = query(q, where('gameId', '==', gameId));
    }
    if (groupId) {
      q = query(q, where('groupId', '==', groupId));
    }
    if (upcoming) {
      q = query(q, where('startDate', '>', Timestamp.now()));
    }

    q = query(q, orderBy('startDate', 'asc'), limit(limitCount));

    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Event));
  }

  static async joinEvent(eventId: string, userId: string): Promise<void> {
    await updateDoc(doc(db, this.COLLECTION, eventId), {
      participantIds: arrayUnion(userId),
      participantsCount: increment(1),
    });
  }

  static async leaveEvent(eventId: string, userId: string): Promise<void> {
    await updateDoc(doc(db, this.COLLECTION, eventId), {
      participantIds: arrayRemove(userId),
      participantsCount: increment(-1),
    });
  }

  static async markInterested(eventId: string, userId: string): Promise<void> {
    await updateDoc(doc(db, this.COLLECTION, eventId), {
      interestedIds: arrayUnion(userId),
      interestedCount: increment(1),
    });
  }
}

// Export all services
export {
  UserProfileService,
  SocialService,
  PostsService,
  GroupsService,
  EventsService,
};

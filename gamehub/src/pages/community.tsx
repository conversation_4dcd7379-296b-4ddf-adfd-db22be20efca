import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Tabs,
  Tab,
  Button,
  Avatar,
  Chip,
  Badge,
  Divider,
  Input,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from '@heroui/react';
import {
  ChatBubbleOvalLeftEllipsisIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  TrophyIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FireIcon,
  SparklesIcon,
  HeartIcon,
  ShareIcon,
  ChatBubbleLeftIcon,
  EllipsisHorizontalIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartSolidIcon,
} from '@heroicons/react/24/solid';

import DefaultLayout from '@/layouts/default';
import { title, subtitle } from '@/components/primitives';
import { useAuth } from '@/contexts/AuthContext';
import { Post, UserProfile, Group, Event } from '@/types/community';
import { PostsService, GroupsService, EventsService } from '@/services/communityService';
import { ChatSystem, ForumSystem, GroupsAndEvents, AchievementSystem } from '@/components/community';

// Mock data for demonstration
const mockPosts: Post[] = [
  {
    id: '1',
    authorId: 'user1',
    authorName: 'GamerPro2024',
    authorAvatar: 'https://i.pravatar.cc/150?u=user1',
    content: 'Acabei de terminar Elden Ring pela terceira vez! Que jogo incrível. Alguém mais viciado nesse mundo? 🎮',
    type: 'text',
    gameId: 'elden-ring',
    gameName: 'Elden Ring',
    tags: ['elden-ring', 'souls-like', 'rpg'],
    likesCount: 24,
    commentsCount: 8,
    sharesCount: 3,
    createdAt: { seconds: Date.now() / 1000 } as any,
    updatedAt: { seconds: Date.now() / 1000 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '2',
    authorId: 'user2',
    authorName: 'StreamerQueen',
    authorAvatar: 'https://i.pravatar.cc/150?u=user2',
    content: 'Live hoje às 20h jogando Valorant! Vamos subir de rank juntos? Drop no chat quem vai aparecer! 🔥',
    type: 'text',
    gameId: 'valorant',
    gameName: 'Valorant',
    tags: ['valorant', 'stream', 'live'],
    likesCount: 45,
    commentsCount: 12,
    sharesCount: 7,
    createdAt: { seconds: (Date.now() - 3600000) / 1000 } as any,
    updatedAt: { seconds: (Date.now() - 3600000) / 1000 } as any,
    isEdited: false,
    visibility: 'public',
  },
];

const mockGroups: Group[] = [
  {
    id: '1',
    name: 'Souls-like Brasil',
    description: 'Comunidade brasileira dedicada aos jogos souls-like',
    avatar: 'https://i.pravatar.cc/150?u=group1',
    banner: '/api/placeholder/400/200',
    category: 'Gaming',
    gameId: 'dark-souls',
    gameName: 'Dark Souls',
    creatorId: 'user1',
    moderatorIds: ['user1', 'user2'],
    memberIds: ['user1', 'user2', 'user3'],
    membersCount: 1247,
    postsCount: 89,
    isPrivate: false,
    requiresApproval: false,
    rules: ['Seja respeitoso', 'Sem spoilers sem aviso', 'Ajude outros jogadores'],
    tags: ['souls-like', 'dark-souls', 'elden-ring'],
    createdAt: { seconds: Date.now() / 1000 } as any,
    updatedAt: { seconds: Date.now() / 1000 } as any,
    lastActivity: { seconds: Date.now() / 1000 } as any,
  },
  {
    id: '2',
    name: 'Valorant Competitivo',
    description: 'Para jogadores que levam Valorant a sério',
    avatar: 'https://i.pravatar.cc/150?u=group2',
    banner: '/api/placeholder/400/200',
    category: 'Competitive',
    gameId: 'valorant',
    gameName: 'Valorant',
    creatorId: 'user2',
    moderatorIds: ['user2'],
    memberIds: ['user1', 'user2', 'user3', 'user4'],
    membersCount: 892,
    postsCount: 156,
    isPrivate: false,
    requiresApproval: true,
    rules: ['Rank mínimo: Ouro', 'Sem toxicidade', 'Compartilhe estratégias'],
    tags: ['valorant', 'competitive', 'fps'],
    createdAt: { seconds: Date.now() / 1000 } as any,
    updatedAt: { seconds: Date.now() / 1000 } as any,
    lastActivity: { seconds: Date.now() / 1000 } as any,
  },
];

const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Torneio de Valorant - Copa GameHub',
    description: 'Torneio competitivo com premiação em dinheiro',
    type: 'tournament',
    gameId: 'valorant',
    gameName: 'Valorant',
    organizerId: 'user1',
    organizerName: 'GamerPro2024',
    startDate: { seconds: (Date.now() + 86400000) / 1000 } as any,
    endDate: { seconds: (Date.now() + 172800000) / 1000 } as any,
    timezone: 'America/Sao_Paulo',
    onlineLink: 'https://discord.gg/gamehub',
    maxParticipants: 64,
    participantIds: ['user1', 'user2'],
    participantsCount: 32,
    interestedIds: ['user3', 'user4'],
    interestedCount: 89,
    tags: ['valorant', 'tournament', 'competitive'],
    isPublic: true,
    status: 'upcoming',
    createdAt: { seconds: Date.now() / 1000 } as any,
    updatedAt: { seconds: Date.now() / 1000 } as any,
  },
];

export default function CommunityPage() {
  const { currentUser } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [selectedTab, setSelectedTab] = useState('feed');
  const [posts, setPosts] = useState<Post[]>(mockPosts);
  const [groups, setGroups] = useState<Group[]>(mockGroups);
  const [events, setEvents] = useState<Event[]>(mockEvents);
  const [loading, setLoading] = useState(false);
  const [newPostContent, setNewPostContent] = useState('');
  const [likedPosts, setLikedPosts] = useState<string[]>([]);
  const [isChatOpen, setIsChatOpen] = useState(false);

  useEffect(() => {
    loadCommunityData();
  }, [selectedTab]);

  const loadCommunityData = async () => {
    setLoading(true);
    try {
      // In production, load real data based on selectedTab
      // For now, using mock data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate loading
    } catch (error) {
      console.error('Error loading community data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePost = async () => {
    if (!currentUser || !newPostContent.trim()) return;

    const newPost: Post = {
      id: `post-${Date.now()}`,
      authorId: currentUser.uid,
      authorName: currentUser.displayName || 'Usuário',
      authorAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      content: newPostContent,
      type: 'text',
      tags: [],
      likesCount: 0,
      commentsCount: 0,
      sharesCount: 0,
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
      isEdited: false,
      visibility: 'public',
    };

    setPosts(prev => [newPost, ...prev]);
    setNewPostContent('');
    onClose();
  };

  const handleLikePost = (postId: string) => {
    if (!currentUser) return;

    const isLiked = likedPosts.includes(postId);
    
    if (isLiked) {
      setLikedPosts(prev => prev.filter(id => id !== postId));
      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { ...post, likesCount: post.likesCount - 1 }
          : post
      ));
    } else {
      setLikedPosts(prev => [...prev, postId]);
      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { ...post, likesCount: post.likesCount + 1 }
          : post
      ));
    }
  };

  const formatTimeAgo = (timestamp: any) => {
    const now = Date.now();
    const postTime = timestamp.seconds * 1000;
    const diff = now - postTime;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days}d atrás`;
    if (hours > 0) return `${hours}h atrás`;
    if (minutes > 0) return `${minutes}m atrás`;
    return 'Agora';
  };

  return (
    <DefaultLayout>
      <div className="flex flex-col gap-6 py-8 md:py-10">
        {/* Header */}
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="text-center max-w-4xl">
            <div className="flex items-center justify-center gap-2 mb-4">
              <UserGroupIcon className="w-8 h-8 text-primary" />
              <Chip color="primary" variant="flat" size="sm">
                Comunidade Gaming
              </Chip>
            </div>
            
            <h1 className={title({ size: "lg" })}>
              Conecte-se com{" "}
              <span className={title({ color: "violet", size: "lg" })}>gamers</span>
            </h1>
            
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Participe de discussões, compartilhe experiências, encontre novos amigos
              e faça parte da maior comunidade gaming do Brasil.
            </p>
          </div>
        </section>

        {/* Quick Actions */}
        {currentUser && (
          <Card>
            <CardBody className="p-4">
              <div className="flex items-center gap-4">
                <Avatar
                  src={currentUser.photoURL || undefined}
                  name={currentUser.displayName || 'U'}
                  size="md"
                />
                <Button
                  variant="flat"
                  className="flex-1 justify-start text-default-500"
                  onClick={onOpen}
                >
                  O que você está jogando hoje?
                </Button>
                <Button
                  color="primary"
                  startContent={<PlusIcon className="w-4 h-4" />}
                  onClick={onOpen}
                >
                  Criar Post
                </Button>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
          }}
        >
          <Tab
            key="feed"
            title={
              <div className="flex items-center gap-2">
                <FireIcon className="w-4 h-4" />
                <span>Feed</span>
              </div>
            }
          >
            <div className="space-y-4">
              {posts.map((post) => (
                <Card key={post.id} className="w-full">
                  <CardBody className="p-6">
                    {/* Post Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={post.authorAvatar}
                          name={post.authorName}
                          size="md"
                        />
                        <div>
                          <p className="font-semibold">{post.authorName}</p>
                          <div className="flex items-center gap-2 text-sm text-default-500">
                            <span>{formatTimeAgo(post.createdAt)}</span>
                            {post.gameName && (
                              <>
                                <span>•</span>
                                <Chip size="sm" color="primary" variant="flat">
                                  {post.gameName}
                                </Chip>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <Button
                        isIconOnly
                        variant="light"
                        size="sm"
                      >
                        <EllipsisHorizontalIcon className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Post Content */}
                    <div className="mb-4">
                      <p className="text-default-700 leading-relaxed">
                        {post.content}
                      </p>
                    </div>

                    {/* Post Tags */}
                    {post.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.map((tag) => (
                          <Chip
                            key={tag}
                            size="sm"
                            variant="flat"
                            color="secondary"
                            className="text-xs"
                          >
                            #{tag}
                          </Chip>
                        ))}
                      </div>
                    )}

                    <Divider className="my-4" />

                    {/* Post Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-6">
                        <Button
                          variant="light"
                          size="sm"
                          startContent={
                            likedPosts.includes(post.id) ? (
                              <HeartSolidIcon className="w-4 h-4 text-red-500" />
                            ) : (
                              <HeartIcon className="w-4 h-4" />
                            )
                          }
                          onClick={() => handleLikePost(post.id)}
                          className={likedPosts.includes(post.id) ? 'text-red-500' : ''}
                        >
                          {post.likesCount}
                        </Button>
                        <Button
                          variant="light"
                          size="sm"
                          startContent={<ChatBubbleLeftIcon className="w-4 h-4" />}
                        >
                          {post.commentsCount}
                        </Button>
                        <Button
                          variant="light"
                          size="sm"
                          startContent={<ShareIcon className="w-4 h-4" />}
                        >
                          {post.sharesCount}
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>

          <Tab
            key="groups"
            title={
              <div className="flex items-center gap-2">
                <UserGroupIcon className="w-4 h-4" />
                <span>Grupos</span>
              </div>
            }
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Grupos Populares</h3>
                <Button
                  color="primary"
                  startContent={<PlusIcon className="w-4 h-4" />}
                  size="sm"
                >
                  Criar Grupo
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {groups.map((group) => (
                  <Card key={group.id} isPressable className="hover:scale-105 transition-transform">
                    <CardBody className="p-0">
                      <div className="relative">
                        <div className="h-24 bg-gradient-to-r from-primary to-secondary rounded-t-lg" />
                        <Avatar
                          src={group.avatar}
                          name={group.name}
                          size="lg"
                          className="absolute -bottom-6 left-4 border-4 border-background"
                        />
                      </div>
                      <div className="p-4 pt-8">
                        <h4 className="font-semibold mb-1">{group.name}</h4>
                        <p className="text-sm text-default-500 mb-3 line-clamp-2">
                          {group.description}
                        </p>
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-4">
                            <span className="text-default-500">
                              {group.membersCount.toLocaleString()} membros
                            </span>
                            <span className="text-default-500">
                              {group.postsCount} posts
                            </span>
                          </div>
                          <Chip
                            size="sm"
                            color={group.isPrivate ? 'warning' : 'success'}
                            variant="flat"
                          >
                            {group.isPrivate ? 'Privado' : 'Público'}
                          </Chip>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-3">
                          {group.tags.slice(0, 3).map((tag) => (
                            <Chip
                              key={tag}
                              size="sm"
                              variant="flat"
                              color="primary"
                              className="text-xs"
                            >
                              #{tag}
                            </Chip>
                          ))}
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            </div>
          </Tab>

          <Tab
            key="events"
            title={
              <div className="flex items-center gap-2">
                <CalendarDaysIcon className="w-4 h-4" />
                <span>Eventos</span>
              </div>
            }
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Próximos Eventos</h3>
                <Button
                  color="primary"
                  startContent={<PlusIcon className="w-4 h-4" />}
                  size="sm"
                >
                  Criar Evento
                </Button>
              </div>

              <div className="space-y-4">
                {events.map((event) => (
                  <Card key={event.id} className="w-full">
                    <CardBody className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <div className="w-16 h-16 bg-primary/10 rounded-lg flex flex-col items-center justify-center">
                            <span className="text-xs text-primary font-medium">
                              {new Date(event.startDate.seconds * 1000).toLocaleDateString('pt-BR', { day: '2-digit' })}
                            </span>
                            <span className="text-xs text-primary">
                              {new Date(event.startDate.seconds * 1000).toLocaleDateString('pt-BR', { month: 'short' })}
                            </span>
                          </div>
                        </div>

                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h4 className="font-semibold text-lg">{event.title}</h4>
                              <p className="text-sm text-default-500">
                                Por {event.organizerName}
                              </p>
                            </div>
                            <Chip
                              size="sm"
                              color="primary"
                              variant="flat"
                            >
                              {event.type}
                            </Chip>
                          </div>

                          <p className="text-default-600 mb-3 line-clamp-2">
                            {event.description}
                          </p>

                          <div className="flex items-center gap-4 text-sm text-default-500 mb-3">
                            <span>
                              📅 {new Date(event.startDate.seconds * 1000).toLocaleDateString('pt-BR')}
                            </span>
                            <span>
                              🕐 {new Date(event.startDate.seconds * 1000).toLocaleTimeString('pt-BR', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                            {event.gameName && (
                              <Chip size="sm" color="secondary" variant="flat">
                                {event.gameName}
                              </Chip>
                            )}
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4 text-sm">
                              <span className="text-success">
                                ✅ {event.participantsCount} participando
                              </span>
                              <span className="text-warning">
                                ⭐ {event.interestedCount} interessados
                              </span>
                            </div>

                            <div className="flex gap-2">
                              <Button size="sm" variant="flat" color="warning">
                                Interessado
                              </Button>
                              <Button size="sm" color="primary">
                                Participar
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            </div>
          </Tab>

          <Tab
            key="achievements"
            title={
              <div className="flex items-center gap-2">
                <TrophyIcon className="w-4 h-4" />
                <span>Conquistas</span>
              </div>
            }
          >
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Suas Conquistas Sociais</h3>
                </CardHeader>
                <CardBody className="pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Mock achievements */}
                    <div className="flex items-center gap-3 p-3 bg-success/10 rounded-lg">
                      <div className="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                        <span className="text-2xl">🎯</span>
                      </div>
                      <div>
                        <p className="font-semibold text-success">Primeiro Post</p>
                        <p className="text-xs text-default-500">Compartilhe sua primeira experiência</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-primary/10 rounded-lg">
                      <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                        <span className="text-2xl">👥</span>
                      </div>
                      <div>
                        <p className="font-semibold text-primary">Sociável</p>
                        <p className="text-xs text-default-500">Siga 10 pessoas</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-warning/10 rounded-lg">
                      <div className="w-12 h-12 bg-warning/20 rounded-full flex items-center justify-center">
                        <span className="text-2xl">💬</span>
                      </div>
                      <div>
                        <p className="font-semibold text-warning">Conversador</p>
                        <p className="text-xs text-default-500">Faça 50 comentários</p>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Ranking da Comunidade</h3>
                </CardHeader>
                <CardBody className="pt-0">
                  <div className="space-y-3">
                    {[
                      { name: 'GamerPro2024', points: 2450, rank: 1, avatar: 'https://i.pravatar.cc/150?u=1' },
                      { name: 'StreamerQueen', points: 2180, rank: 2, avatar: 'https://i.pravatar.cc/150?u=2' },
                      { name: 'NoobMaster', points: 1890, rank: 3, avatar: 'https://i.pravatar.cc/150?u=3' },
                    ].map((user) => (
                      <div key={user.rank} className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                            user.rank === 1 ? 'bg-yellow-500 text-white' :
                            user.rank === 2 ? 'bg-gray-400 text-white' :
                            user.rank === 3 ? 'bg-orange-600 text-white' :
                            'bg-default-200'
                          }`}>
                            {user.rank}
                          </div>
                          <Avatar src={user.avatar} name={user.name} size="sm" />
                          <span className="font-medium">{user.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-primary">{user.points.toLocaleString()}</p>
                          <p className="text-xs text-default-500">pontos</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </div>
          </Tab>

          <Tab
            key="forums"
            title={
              <div className="flex items-center gap-2">
                <ChatBubbleLeftRightIcon className="w-4 h-4" />
                <span>Fóruns</span>
              </div>
            }
          >
            <ForumSystem />
          </Tab>

          <Tab
            key="groups-events"
            title={
              <div className="flex items-center gap-2">
                <UserGroupIcon className="w-4 h-4" />
                <span>Grupos & Eventos</span>
              </div>
            }
          >
            <GroupsAndEvents />
          </Tab>

          <Tab
            key="achievements-full"
            title={
              <div className="flex items-center gap-2">
                <TrophyIcon className="w-4 h-4" />
                <span>Sistema de Conquistas</span>
              </div>
            }
          >
            <AchievementSystem />
          </Tab>
        </Tabs>

        {/* Floating Chat Button */}
        {currentUser && (
          <Button
            isIconOnly
            color="primary"
            size="lg"
            className="fixed bottom-6 right-6 z-40 shadow-lg"
            onClick={() => setIsChatOpen(!isChatOpen)}
          >
            <ChatBubbleOvalLeftEllipsisIcon className="w-6 h-6" />
          </Button>
        )}

        {/* Chat System */}
        <ChatSystem
          isOpen={isChatOpen}
          onClose={() => setIsChatOpen(false)}
        />

        {/* Create Post Modal */}
        <Modal
          isOpen={isOpen}
          onClose={onClose}
          size="2xl"
          scrollBehavior="inside"
        >
          <ModalContent>
            <ModalHeader>
              <div className="flex items-center gap-2">
                <SparklesIcon className="w-5 h-5" />
                <span>Criar Post</span>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar
                    src={currentUser?.photoURL || undefined}
                    name={currentUser?.displayName || 'U'}
                    size="md"
                  />
                  <div>
                    <p className="font-semibold">{currentUser?.displayName || 'Usuário'}</p>
                    <p className="text-sm text-default-500">Público</p>
                  </div>
                </div>

                <Textarea
                  placeholder="O que você está jogando hoje? Compartilhe sua experiência..."
                  value={newPostContent}
                  onChange={(e) => setNewPostContent(e.target.value)}
                  minRows={4}
                  maxRows={8}
                  maxLength={500}
                />

                <div className="flex items-center justify-between text-sm text-default-500">
                  <span>{newPostContent.length}/500 caracteres</span>
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                onPress={handleCreatePost}
                isDisabled={!newPostContent.trim()}
              >
                Publicar
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ody,
  CardHeader,
  <PERSON><PERSON>,
  <PERSON>b,
  <PERSON><PERSON>,
  Badge,
  useDisclosure,
  <PERSON><PERSON>,
  Chip,
} from '@heroui/react';
import {
  MagnifyingGlassIcon,
  UsersIcon,
  HeartIcon,
  PlayIcon,
  FireIcon,
  TrophyIcon,
  ChartBarIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartSolidIcon,
} from '@heroicons/react/24/solid';

import DefaultLayout from '@/layouts/default';
import { title, subtitle } from '@/components/primitives';
import { useAuth } from '@/contexts/AuthContext';
import { GameService } from '@/services/firestore';
import { Game } from '@/types/database';
import { mockGames, popularGenres, popularPlatforms } from '@/data/mockGames';
import { GameCard, GameFilters, GameDetailsModal, GameStats } from '@/components/games';

export default function GamesPage() {
  const { currentUser } = useAuth();
  const { isO<PERSON>, onO<PERSON>, onClose } = useDisclosure();
  
  // State management
  const [games, setGames] = useState<Game[]>([]);
  const [popularGames, setPopularGames] = useState<Game[]>([]);
  const [favoriteGames, setFavoriteGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('discover');
  const [selectedGenre, setSelectedGenre] = useState('');
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [minRating, setMinRating] = useState('');
  const [sortBy, setSortBy] = useState('playerCount');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [userFavorites, setUserFavorites] = useState<string[]>([]);

  // Load data on component mount
  useEffect(() => {
    loadGames();
    loadPopularGames();
    if (currentUser) {
      loadFavoriteGames();
    }
  }, [currentUser]);

  // Load games with filters
  useEffect(() => {
    if (selectedTab === 'discover') {
      loadGames();
    }
  }, [searchTerm, selectedGenre, selectedPlatform, minRating, sortBy, sortOrder, selectedTab]);

  const loadGames = async () => {
    try {
      setLoading(true);
      
      // For demo purposes, use mock data
      // In production, this would call GameService.getGames()
      let filteredGames = [...mockGames] as Game[];
      
      // Apply filters
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        filteredGames = filteredGames.filter(game =>
          game.title.toLowerCase().includes(searchLower) ||
          game.developer.toLowerCase().includes(searchLower) ||
          game.genres.some(genre => genre.toLowerCase().includes(searchLower))
        );
      }
      
      if (selectedGenre) {
        filteredGames = filteredGames.filter(game =>
          game.genres.includes(selectedGenre)
        );
      }
      
      if (selectedPlatform) {
        filteredGames = filteredGames.filter(game =>
          game.platforms.includes(selectedPlatform)
        );
      }
      
      if (minRating) {
        filteredGames = filteredGames.filter(game =>
          game.rating >= parseFloat(minRating)
        );
      }
      
      // Apply sorting
      filteredGames.sort((a, b) => {
        const aValue = a[sortBy as keyof Game] as number;
        const bValue = b[sortBy as keyof Game] as number;
        return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
      });
      
      // Add mock IDs
      filteredGames = filteredGames.map((game, index) => ({
        ...game,
        id: `game-${index}`
      }));
      
      setGames(filteredGames);
    } catch (error) {
      console.error('Error loading games:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPopularGames = async () => {
    try {
      // For demo purposes, use mock data
      const popular = [...mockGames]
        .sort((a, b) => b.playerCount - a.playerCount)
        .slice(0, 6)
        .map((game, index) => ({ ...game, id: `popular-${index}` }));
      
      setPopularGames(popular);
    } catch (error) {
      console.error('Error loading popular games:', error);
    }
  };

  const loadFavoriteGames = async () => {
    if (!currentUser) return;
    
    try {
      // For demo purposes, use mock data
      // In production, this would call GameService.getUserFavoriteGames()
      const favorites = mockGames.slice(0, 3).map((game, index) => ({
        ...game,
        id: `favorite-${index}`
      }));
      
      setFavoriteGames(favorites);
      setUserFavorites(favorites.map(game => game.id));
    } catch (error) {
      console.error('Error loading favorite games:', error);
    }
  };

  const handleToggleFavorite = async (gameId: string) => {
    if (!currentUser) return;
    
    try {
      const isFavorite = userFavorites.includes(gameId);
      
      if (isFavorite) {
        // Remove from favorites
        await GameService.removeFromFavorites(currentUser.uid, gameId);
        setUserFavorites(prev => prev.filter(id => id !== gameId));
      } else {
        // Add to favorites
        await GameService.addToFavorites(currentUser.uid, gameId);
        setUserFavorites(prev => [...prev, gameId]);
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const handleGameClick = (game: Game) => {
    setSelectedGame(game);
    onOpen();
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedGenre('');
    setSelectedPlatform('');
    setMinRating('');
    setSortBy('playerCount');
    setSortOrder('desc');
  };

  const formatPlayerCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(0)}K`;
    }
    return count.toString();
  };

  return (
    <DefaultLayout>
      <div className="flex flex-col gap-6 py-8 md:py-10">
        {/* Header */}
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="text-center max-w-4xl">
            <div className="flex items-center justify-center gap-2 mb-4">
              <PlayIcon className="w-8 h-8 text-primary" />
              <Chip color="primary" variant="flat" size="sm">
                Catálogo Completo
              </Chip>
            </div>
            
            <h1 className={title({ size: "lg" })}>
              Descubra os melhores{" "}
              <span className={title({ color: "violet", size: "lg" })}>jogos</span>
            </h1>
            
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Explore nossa biblioteca completa de jogos, encontre seus favoritos
              e conecte-se com outros jogadores da comunidade.
            </p>
          </div>
        </section>

        {/* Search and Filters */}
        <GameFilters
          searchTerm={searchTerm}
          selectedGenre={selectedGenre}
          selectedPlatform={selectedPlatform}
          minRating={minRating}
          sortBy={sortBy}
          sortOrder={sortOrder}
          genres={popularGenres}
          platforms={popularPlatforms}
          onSearchChange={setSearchTerm}
          onGenreChange={setSelectedGenre}
          onPlatformChange={setSelectedPlatform}
          onMinRatingChange={setMinRating}
          onSortChange={(sortBy, sortOrder) => {
            setSortBy(sortBy);
            setSortOrder(sortOrder);
          }}
          onClearFilters={clearFilters}
        />

        {/* Tabs */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
          }}
        >
          <Tab
            key="discover"
            title={
              <div className="flex items-center gap-2">
                <MagnifyingGlassIcon className="w-4 h-4" />
                <span>Descobrir</span>
              </div>
            }
          >
            {/* Popular Games Section */}
            <div className="space-y-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FireIcon className="w-5 h-5 text-orange-500" />
                    <h3 className="text-lg font-semibold">Jogos Populares</h3>
                  </div>
                  <Chip color="warning" variant="flat" size="sm">
                    Em Alta
                  </Chip>
                </CardHeader>
                <CardBody className="pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {popularGames.map((game) => (
                      <GameCard
                        key={game.id}
                        game={game}
                        variant="compact"
                        isFavorite={userFavorites.includes(game.id)}
                        showFavoriteButton={!!currentUser}
                        onGameClick={handleGameClick}
                        onToggleFavorite={handleToggleFavorite}
                      />
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* All Games Grid */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between w-full">
                    <h3 className="text-lg font-semibold">Todos os Jogos</h3>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-default-500">
                        {games.length} jogos encontrados
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardBody className="pt-0">
                  {loading ? (
                    <div className="flex justify-center py-8">
                      <Spinner size="lg" />
                    </div>
                  ) : games.length === 0 ? (
                    <div className="text-center py-8">
                      <PlayIcon className="w-12 h-12 text-default-300 mx-auto mb-4" />
                      <p className="text-default-500">
                        Nenhum jogo encontrado com os filtros aplicados.
                      </p>
                      <Button
                        color="primary"
                        variant="flat"
                        onClick={clearFilters}
                        className="mt-4"
                      >
                        Limpar Filtros
                      </Button>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {games.map((game) => (
                        <GameCard
                          key={game.id}
                          game={game}
                          variant="detailed"
                          isFavorite={userFavorites.includes(game.id)}
                          showFavoriteButton={!!currentUser}
                          onGameClick={handleGameClick}
                          onToggleFavorite={handleToggleFavorite}
                        />
                      ))}
                    </div>
                  )}
                </CardBody>
              </Card>
            </div>
          </Tab>

          {currentUser && (
            <Tab
              key="favorites"
              title={
                <div className="flex items-center gap-2">
                  <HeartIcon className="w-4 h-4" />
                  <span>Favoritos</span>
                  <Badge content={favoriteGames.length} color="danger" size="sm" />
                </div>
              }
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <HeartSolidIcon className="w-5 h-5 text-red-500" />
                    <h3 className="text-lg font-semibold">Meus Jogos Favoritos</h3>
                  </div>
                </CardHeader>
                <CardBody className="pt-0">
                  {favoriteGames.length === 0 ? (
                    <div className="text-center py-8">
                      <HeartIcon className="w-12 h-12 text-default-300 mx-auto mb-4" />
                      <p className="text-default-500 mb-2">
                        Você ainda não tem jogos favoritos.
                      </p>
                      <p className="text-sm text-default-400 mb-4">
                        Explore jogos e adicione aos favoritos clicando no ❤️
                      </p>
                      <Button
                        color="primary"
                        variant="flat"
                        onClick={() => setSelectedTab('discover')}
                      >
                        Descobrir Jogos
                      </Button>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {favoriteGames.map((game) => (
                        <GameCard
                          key={game.id}
                          game={game}
                          variant="compact"
                          isFavorite={true}
                          showFavoriteButton={true}
                          onGameClick={handleGameClick}
                          onToggleFavorite={handleToggleFavorite}
                        />
                      ))}
                    </div>
                  )}
                </CardBody>
              </Card>
            </Tab>
          )}

          <Tab
            key="stats"
            title={
              <div className="flex items-center gap-2">
                <ChartBarIcon className="w-4 h-4" />
                <span>Estatísticas</span>
              </div>
            }
          >
            <GameStats
              games={mockGames as Game[]}
              genres={popularGenres}
              platforms={popularPlatforms}
            />
          </Tab>
        </Tabs>

        {/* Game Details Modal */}
        <GameDetailsModal
          isOpen={isOpen}
          onClose={onClose}
          game={selectedGame}
          isFavorite={selectedGame ? userFavorites.includes(selectedGame.id) : false}
          onToggleFavorite={handleToggleFavorite}
          onGenreClick={(genre) => {
            setSelectedGenre(genre);
            setSelectedTab('discover');
          }}
          onPlatformClick={(platform) => {
            setSelectedPlatform(platform);
            setSelectedTab('discover');
          }}
        />
      </div>
    </DefaultLayout>
  );
}

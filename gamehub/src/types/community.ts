import { Timestamp } from 'firebase/firestore';

// User Profile and Social
export interface UserProfile {
  id: string;
  username: string;
  displayName: string;
  avatar: string;
  bio: string;
  location?: string;
  favoriteGames: string[];
  preferredGenres: string[];
  platforms: string[];
  joinDate: Timestamp;
  lastActive: Timestamp;
  isOnline: boolean;
  
  // Social stats
  followersCount: number;
  followingCount: number;
  postsCount: number;
  reputation: number;
  level: number;
  
  // Privacy settings
  isProfilePublic: boolean;
  allowDirectMessages: boolean;
  showOnlineStatus: boolean;
}

// Social Connections
export interface Follow {
  id: string;
  followerId: string;
  followingId: string;
  createdAt: Timestamp;
}

// Posts and Content
export interface Post {
  id: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'link' | 'achievement' | 'review';
  gameId?: string;
  gameName?: string;
  images?: string[];
  videoUrl?: string;
  linkUrl?: string;
  linkTitle?: string;
  linkDescription?: string;
  tags: string[];
  likesCount: number;
  commentsCount: number;
  sharesCount: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isEdited: boolean;
  visibility: 'public' | 'followers' | 'friends';
}

// Comments
export interface Comment {
  id: string;
  postId: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  content: string;
  parentCommentId?: string; // For nested comments
  likesCount: number;
  repliesCount: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isEdited: boolean;
}

// Likes
export interface Like {
  id: string;
  userId: string;
  targetId: string; // postId or commentId
  targetType: 'post' | 'comment';
  createdAt: Timestamp;
}

// Groups
export interface Group {
  id: string;
  name: string;
  description: string;
  avatar: string;
  banner: string;
  category: string;
  gameId?: string;
  gameName?: string;
  creatorId: string;
  moderatorIds: string[];
  memberIds: string[];
  membersCount: number;
  postsCount: number;
  isPrivate: boolean;
  requiresApproval: boolean;
  rules: string[];
  tags: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastActivity: Timestamp;
}

// Group Membership
export interface GroupMembership {
  id: string;
  groupId: string;
  userId: string;
  role: 'member' | 'moderator' | 'admin';
  joinedAt: Timestamp;
  status: 'active' | 'pending' | 'banned';
}

// Events
export interface Event {
  id: string;
  title: string;
  description: string;
  type: 'tournament' | 'meetup' | 'stream' | 'discussion' | 'workshop';
  gameId?: string;
  gameName?: string;
  organizerId: string;
  organizerName: string;
  groupId?: string;
  groupName?: string;
  startDate: Timestamp;
  endDate: Timestamp;
  timezone: string;
  location?: string; // For physical events
  onlineLink?: string; // For online events
  maxParticipants?: number;
  participantIds: string[];
  participantsCount: number;
  interestedIds: string[];
  interestedCount: number;
  tags: string[];
  isPublic: boolean;
  status: 'upcoming' | 'live' | 'ended' | 'cancelled';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Chat and Messages
export interface ChatRoom {
  id: string;
  name?: string;
  type: 'direct' | 'group' | 'public';
  participantIds: string[];
  creatorId: string;
  lastMessageId?: string;
  lastMessageAt?: Timestamp;
  isActive: boolean;
  createdAt: Timestamp;
}

export interface Message {
  id: string;
  chatRoomId: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  replyToId?: string; // For message replies
  isEdited: boolean;
  readBy: string[]; // User IDs who read the message
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Forums
export interface Forum {
  id: string;
  name: string;
  description: string;
  category: string;
  gameId?: string;
  gameName?: string;
  moderatorIds: string[];
  topicsCount: number;
  postsCount: number;
  lastTopicId?: string;
  lastPostAt?: Timestamp;
  isActive: boolean;
  order: number;
  createdAt: Timestamp;
}

export interface ForumTopic {
  id: string;
  forumId: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  tags: string[];
  isPinned: boolean;
  isLocked: boolean;
  viewsCount: number;
  repliesCount: number;
  lastReplyId?: string;
  lastReplyAt?: Timestamp;
  lastReplyAuthor?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ForumPost {
  id: string;
  topicId: string;
  forumId: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  isEdited: boolean;
  editedAt?: Timestamp;
  likesCount: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Achievements and Gamification
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'social' | 'content' | 'community' | 'special';
  type: 'bronze' | 'silver' | 'gold' | 'platinum' | 'legendary';
  points: number;
  requirements: {
    type: string;
    target: number;
    current?: number;
  }[];
  isSecret: boolean;
  unlockedBy: string[]; // User IDs
  createdAt: Timestamp;
}

export interface UserAchievement {
  id: string;
  userId: string;
  achievementId: string;
  unlockedAt: Timestamp;
  progress: number; // 0-100
}

// Reputation and Badges
export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  category: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  requirements: string;
  isActive: boolean;
  createdAt: Timestamp;
}

export interface UserBadge {
  id: string;
  userId: string;
  badgeId: string;
  earnedAt: Timestamp;
  isDisplayed: boolean;
}

// Notifications
export interface Notification {
  id: string;
  userId: string;
  type: 'like' | 'comment' | 'follow' | 'message' | 'mention' | 'event' | 'achievement' | 'group_invite';
  title: string;
  message: string;
  actionUrl?: string;
  fromUserId?: string;
  fromUserName?: string;
  fromUserAvatar?: string;
  relatedId?: string; // postId, commentId, etc.
  isRead: boolean;
  createdAt: Timestamp;
}

// Activity Feed
export interface Activity {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  type: 'post' | 'comment' | 'like' | 'follow' | 'join_group' | 'achievement' | 'event_join';
  action: string;
  targetId?: string;
  targetType?: string;
  targetName?: string;
  gameId?: string;
  gameName?: string;
  groupId?: string;
  groupName?: string;
  visibility: 'public' | 'followers' | 'friends';
  createdAt: Timestamp;
}

// Mentorship
export interface MentorshipRequest {
  id: string;
  menteeId: string;
  mentorId: string;
  gameId?: string;
  gameName?: string;
  message: string;
  status: 'pending' | 'accepted' | 'declined' | 'completed';
  createdAt: Timestamp;
  respondedAt?: Timestamp;
  completedAt?: Timestamp;
}

export interface MentorProfile {
  userId: string;
  isAvailable: boolean;
  specialties: string[]; // Game genres or specific games
  experience: string;
  rating: number;
  reviewsCount: number;
  menteeCount: number;
  languages: string[];
  timezone: string;
  availability: {
    [key: string]: string[]; // day: hours
  };
}

// Search and Discovery
export interface SearchFilters {
  query?: string;
  type?: 'users' | 'posts' | 'groups' | 'events' | 'forums';
  gameId?: string;
  category?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: string;
  tags?: string[];
}

// Real-time presence
export interface UserPresence {
  userId: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: Timestamp;
  currentActivity?: string;
  currentGame?: string;
}

import { Route, Routes } from "react-router-dom";

import IndexPage from "@/pages/index";
import GamesPage from "@/pages/games";
import CommunityPage from "@/pages/community";
import ProfilePage from "@/pages/profile";
import DocsPage from "@/pages/docs";
import PricingPage from "@/pages/pricing";
import BlogPage from "@/pages/blog";
import AboutPage from "@/pages/about";

function App() {
  return (
    <Routes>
      <Route element={<IndexPage />} path="/" />
      <Route element={<GamesPage />} path="/games" />
      <Route element={<CommunityPage />} path="/community" />
      <Route element={<ProfilePage />} path="/profile" />
      <Route element={<DocsPage />} path="/docs" />
      <Route element={<PricingPage />} path="/pricing" />
      <Route element={<BlogPage />} path="/blog" />
      <Route element={<AboutPage />} path="/about" />
    </Routes>
  );
}

export default App;

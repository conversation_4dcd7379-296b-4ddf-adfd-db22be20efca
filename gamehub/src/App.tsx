import { Route, Routes } from "react-router-dom";

import IndexPage from "@/pages/index";
import GamesPage from "@/pages/games";
import FeedPage from "@/pages/feed";
import GroupsPage from "@/pages/groups";
import TournamentsPage from "@/pages/tournaments";
import RankingPage from "@/pages/ranking";
import ProfilePage from "@/pages/profile";
import AchievementsPage from "@/pages/achievements";
import DocsPage from "@/pages/docs";
import PricingPage from "@/pages/pricing";
import BlogPage from "@/pages/blog";
import AboutPage from "@/pages/about";

function App() {
  return (
    <Routes>
      <Route element={<IndexPage />} path="/" />
      <Route element={<GamesPage />} path="/games" />
      <Route element={<FeedPage />} path="/feed" />
      <Route element={<GroupsPage />} path="/groups" />
      <Route element={<TournamentsPage />} path="/tournaments" />
      <Route element={<RankingPage />} path="/ranking" />
      <Route element={<ProfilePage />} path="/profile" />
      <Route element={<AchievementsPage />} path="/achievements" />
      <Route element={<DocsPage />} path="/docs" />
      <Route element={<PricingPage />} path="/pricing" />
      <Route element={<BlogPage />} path="/blog" />
      <Route element={<AboutPage />} path="/about" />
    </Routes>
  );
}

export default App;

import { Game } from '@/types/database';
import { Timestamp } from 'firebase/firestore';

// Mock data for popular games
export const mockGames: Omit<Game, 'id'>[] = [
  {
    title: 'Counter-Strike 2',
    developer: 'Valve Corporation',
    publisher: 'Valve Corporation',
    platforms: ['PC', 'Steam'],
    genres: ['FPS', 'Tactical Shooter', 'Competitive'],
    releaseDate: Timestamp.fromDate(new Date('2023-09-27')),
    description: 'O jogo de tiro tático mais popular do mundo, agora com gráficos aprimorados e nova engine Source 2.',
    images: {
      cover: 'https://cdn.akamai.steamstatic.com/steam/apps/730/header.jpg',
      banner: 'https://cdn.akamai.steamstatic.com/steam/apps/730/library_hero.jpg',
      screenshots: [
        'https://cdn.akamai.steamstatic.com/steam/apps/730/ss_d830cdf6c0b6b1c8c1b8f7c5b5c5b5c5b5c5b5c5.1920x1080.jpg',
        'https://cdn.akamai.steamstatic.com/steam/apps/730/ss_d830cdf6c0b6b1c8c1b8f7c5b5c5b5c5b5c5b5c6.1920x1080.jpg'
      ]
    },
    rating: 4.5,
    playerCount: 850000,
    isActive: true,
    tags: ['Multiplayer', 'Competitive', 'Esports', 'Team-based'],
    externalIds: {
      steam: '730'
    }
  },
  {
    title: 'Valorant',
    developer: 'Riot Games',
    publisher: 'Riot Games',
    platforms: ['PC'],
    genres: ['FPS', 'Tactical Shooter', 'Hero Shooter'],
    releaseDate: Timestamp.fromDate(new Date('2020-06-02')),
    description: 'Jogo de tiro tático 5v5 com agentes únicos, cada um com habilidades especiais que complementam o gameplay.',
    images: {
      cover: 'https://images.contentstack.io/v3/assets/bltb6530b271fddd0b1/blt5a87c6b6b6b6b6b6/valorant-cover.jpg',
      banner: 'https://images.contentstack.io/v3/assets/bltb6530b271fddd0b1/blt5a87c6b6b6b6b6b7/valorant-banner.jpg',
      screenshots: [
        'https://images.contentstack.io/v3/assets/bltb6530b271fddd0b1/blt5a87c6b6b6b6b6b8/valorant-screenshot1.jpg',
        'https://images.contentstack.io/v3/assets/bltb6530b271fddd0b1/blt5a87c6b6b6b6b6b9/valorant-screenshot2.jpg'
      ]
    },
    rating: 4.3,
    playerCount: 650000,
    isActive: true,
    tags: ['Multiplayer', 'Competitive', 'Esports', 'Abilities'],
    externalIds: {}
  },
  {
    title: 'League of Legends',
    developer: 'Riot Games',
    publisher: 'Riot Games',
    platforms: ['PC'],
    genres: ['MOBA', 'Strategy', 'Multiplayer'],
    releaseDate: Timestamp.fromDate(new Date('2009-10-27')),
    description: 'O MOBA mais popular do mundo com mais de 160 campeões únicos e constantes atualizações.',
    images: {
      cover: 'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_0.jpg',
      banner: 'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_1.jpg',
      screenshots: [
        'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_2.jpg',
        'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_3.jpg'
      ]
    },
    rating: 4.2,
    playerCount: 1200000,
    isActive: true,
    tags: ['Multiplayer', 'Competitive', 'Esports', 'Strategy'],
    externalIds: {}
  },
  {
    title: 'Fortnite',
    developer: 'Epic Games',
    publisher: 'Epic Games',
    platforms: ['PC', 'PlayStation', 'Xbox', 'Nintendo Switch', 'Mobile'],
    genres: ['Battle Royale', 'Shooter', 'Building'],
    releaseDate: Timestamp.fromDate(new Date('2017-07-25')),
    description: 'Battle Royale com mecânicas de construção únicas e eventos especiais constantes.',
    images: {
      cover: 'https://cdn2.unrealengine.com/fortnite-cover.jpg',
      banner: 'https://cdn2.unrealengine.com/fortnite-banner.jpg',
      screenshots: [
        'https://cdn2.unrealengine.com/fortnite-screenshot1.jpg',
        'https://cdn2.unrealengine.com/fortnite-screenshot2.jpg'
      ]
    },
    rating: 4.0,
    playerCount: 400000,
    isActive: true,
    tags: ['Battle Royale', 'Building', 'Cross-platform', 'Events'],
    externalIds: {
      epic: 'fortnite'
    }
  },
  {
    title: 'Apex Legends',
    developer: 'Respawn Entertainment',
    publisher: 'Electronic Arts',
    platforms: ['PC', 'PlayStation', 'Xbox', 'Nintendo Switch'],
    genres: ['Battle Royale', 'FPS', 'Hero Shooter'],
    releaseDate: Timestamp.fromDate(new Date('2019-02-04')),
    description: 'Battle Royale com personagens únicos (Legends) e mecânicas de movimento fluidas.',
    images: {
      cover: 'https://media.contentapi.ea.com/content/dam/apex-legends/images/2019/01/apex-cover.jpg',
      banner: 'https://media.contentapi.ea.com/content/dam/apex-legends/images/2019/01/apex-banner.jpg',
      screenshots: [
        'https://media.contentapi.ea.com/content/dam/apex-legends/images/2019/01/apex-screenshot1.jpg',
        'https://media.contentapi.ea.com/content/dam/apex-legends/images/2019/01/apex-screenshot2.jpg'
      ]
    },
    rating: 4.1,
    playerCount: 320000,
    isActive: true,
    tags: ['Battle Royale', 'Legends', 'Fast-paced', 'Team-based'],
    externalIds: {
      steam: '1172470',
      origin: 'apex-legends'
    }
  },
  {
    title: 'Minecraft',
    developer: 'Mojang Studios',
    publisher: 'Microsoft',
    platforms: ['PC', 'PlayStation', 'Xbox', 'Nintendo Switch', 'Mobile'],
    genres: ['Sandbox', 'Survival', 'Creative'],
    releaseDate: Timestamp.fromDate(new Date('2011-11-18')),
    description: 'Jogo de construção e sobrevivência em mundo aberto com possibilidades infinitas.',
    images: {
      cover: 'https://www.minecraft.net/content/dam/games/minecraft/key-art/minecraft-cover.jpg',
      banner: 'https://www.minecraft.net/content/dam/games/minecraft/key-art/minecraft-banner.jpg',
      screenshots: [
        'https://www.minecraft.net/content/dam/games/minecraft/screenshots/minecraft-screenshot1.jpg',
        'https://www.minecraft.net/content/dam/games/minecraft/screenshots/minecraft-screenshot2.jpg'
      ]
    },
    rating: 4.6,
    playerCount: 280000,
    isActive: true,
    tags: ['Sandbox', 'Creative', 'Multiplayer', 'Cross-platform'],
    externalIds: {}
  },
  {
    title: 'Rocket League',
    developer: 'Psyonix',
    publisher: 'Epic Games',
    platforms: ['PC', 'PlayStation', 'Xbox', 'Nintendo Switch'],
    genres: ['Sports', 'Racing', 'Multiplayer'],
    releaseDate: Timestamp.fromDate(new Date('2015-07-07')),
    description: 'Futebol com carros! Jogo único que combina futebol com corrida em partidas emocionantes.',
    images: {
      cover: 'https://cdn2.unrealengine.com/rocket-league-cover.jpg',
      banner: 'https://cdn2.unrealengine.com/rocket-league-banner.jpg',
      screenshots: [
        'https://cdn2.unrealengine.com/rocket-league-screenshot1.jpg',
        'https://cdn2.unrealengine.com/rocket-league-screenshot2.jpg'
      ]
    },
    rating: 4.4,
    playerCount: 180000,
    isActive: true,
    tags: ['Sports', 'Cars', 'Competitive', 'Cross-platform'],
    externalIds: {
      steam: '252950',
      epic: 'rocket-league'
    }
  },
  {
    title: 'Grand Theft Auto V',
    developer: 'Rockstar North',
    publisher: 'Rockstar Games',
    platforms: ['PC', 'PlayStation', 'Xbox'],
    genres: ['Action', 'Open World', 'Crime'],
    releaseDate: Timestamp.fromDate(new Date('2013-09-17')),
    description: 'Jogo de ação em mundo aberto com três protagonistas e modo online expansivo.',
    images: {
      cover: 'https://media.rockstargames.com/rockstargames/img/global/news/upload/gtav-cover.jpg',
      banner: 'https://media.rockstargames.com/rockstargames/img/global/news/upload/gtav-banner.jpg',
      screenshots: [
        'https://media.rockstargames.com/rockstargames/img/global/news/upload/gtav-screenshot1.jpg',
        'https://media.rockstargames.com/rockstargames/img/global/news/upload/gtav-screenshot2.jpg'
      ]
    },
    rating: 4.5,
    playerCount: 220000,
    isActive: true,
    tags: ['Open World', 'Crime', 'Multiplayer', 'Story'],
    externalIds: {
      steam: '271590'
    }
  }
];

// Popular genres for filtering
export const popularGenres = [
  'FPS',
  'MOBA',
  'Battle Royale',
  'RPG',
  'Strategy',
  'Sports',
  'Racing',
  'Sandbox',
  'Action',
  'Adventure',
  'Simulation',
  'Puzzle'
];

// Popular platforms for filtering
export const popularPlatforms = [
  'PC',
  'PlayStation',
  'Xbox',
  'Nintendo Switch',
  'Mobile',
  'Steam',
  'Epic Games'
];
